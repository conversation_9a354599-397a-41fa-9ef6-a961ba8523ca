{"nav": {"createCharacter": "Create Character", "dailyTasks": "Daily Tasks", "battlePass": "Battle Pass", "home": "Home", "chats": "Chats", "contacts": "Contacts", "discover": "Discover", "profile": "Profile"}, "common": {"tagline": "A warm AI companion", "streak": "Interaction Streak", "days": "days", "recentChats": "Recent Chats", "viewAllChats": "View all chats", "promoTitle": "Unlock Exclusive Whisper Space", "promoSubtitle": "Upgrade to <PERSON><PERSON> for unlimited AI interactions", "upgradeNow": "Upgrade Now", "member": "Member", "searchPlaceholder": "Search for characters, designers...", "loading": "Loading"}, "sidebar": {"interactionStreak": "Interaction Streak", "todaysJourney": "Today's Journey", "viewJourney": "View Journey", "recentChats": "Recent Chats", "discoverMore": "Discover More", "shiningMoments": "Shining Moments", "discoverCharacters": "Discover Characters", "memoriesSquare": "Memories Square", "community": "Community", "usefulLinks": "Useful Links", "termsOfUse": "Terms of Use", "privacyPolicy": "Privacy Policy", "sitemap": "Sitemap", "contactUs": "Contact Us", "freeUser": "Free User", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout"}, "tasks": {"title": "Task Center", "subtitle": "Complete daily activities to earn rewards and maintain your interaction streak", "daily": "Daily Tasks", "weekly": "Weekly Tasks", "monthly": "Monthly Tasks", "completed": "Completed", "inProgress": "In Progress", "locked": "Locked", "claimReward": "<PERSON><PERSON><PERSON>", "goToGifts": "View Rewards", "dailyGifts": "Daily Gifts", "weeklyGifts": "Weekly Gifts", "monthlyGifts": "Monthly Gifts", "overview": {"todayProgress": "Today's Progress", "tasksCompleted": "{{completed}}/{{total}} Tasks Completed", "streakDays": "{{days}} Day Streak", "nextMilestone": "{{days}} days until next milestone", "weeklyProgress": "Weekly Progress", "monthlyProgress": "Monthly Progress"}, "categories": {"interaction": "Interaction", "creation": "Creation", "social": "Social", "exploration": "Exploration", "memory": "Memory", "bonding": "Bonding"}, "types": {"morningGreeting": {"name": "Morning Greeting", "description": "Start your day by greeting an AI character"}, "topicExplorer": {"name": "Topic Explorer", "description": "Discuss different topics with 3 different AI characters"}, "listeningMoment": {"name": "Listening Moment", "description": "Have a continuous conversation for over 5 minutes"}, "memoryKeeper": {"name": "Memory Keeper", "description": "Save 1 new memory to your memory capsule today"}, "streakMaintainer": {"name": "Streak Maintainer", "description": "Complete your daily interaction to maintain streak"}, "heartGiver": {"name": "Heart Giver", "description": "Send virtual gifts to AI characters"}, "communityExplorer": {"name": "Community Explorer", "description": "Browse and like 3 community character cards"}, "sceneSwitcher": {"name": "Scene Switcher", "description": "Try different interaction scenes with AI characters"}, "storyExperiencer": {"name": "Story Experiencer", "description": "Experience story segments generated by Story Agent"}, "bondBuilder": {"name": "Bond Builder", "description": "Increase intimacy level with 3 different characters"}, "memoryWeaver": {"name": "<PERSON>", "description": "Save 10 meaningful memories this week"}, "loyalCompanion": {"name": "Loyal Companion", "description": "Spend quality time with the same character for hours"}, "noviceCreator": {"name": "<PERSON>ce Creator", "description": "Publish 1 new character card and get positive feedback"}, "storyteller": {"name": "Storyteller", "description": "Create and publish 1 short story this week"}, "socialSharer": {"name": "Social Sharer", "description": "Share platform content on external social media"}, "soulmate": {"name": "Soulmate", "description": "Reach a new major intimacy level with any character"}, "memoryGuardian": {"name": "Memory Guardian", "description": "Build comprehensive memory collection with high accuracy"}, "epicQuestCompleter": {"name": "Epic Quest Completer", "description": "Complete multi-week story campaigns and major achievements"}}, "rewards": {"alphane": "{{amount}} Glimmering Dust", "endora": "{{amount}} Joy Crystal", "serotile": "{{amount}} Memory Puzzle", "oxytol": "{{amount}} <PERSON> Dew", "experience": "{{amount}} Experience", "badge": "Exclusive Badge", "streakFreeze": "Streak Freeze Card"}, "difficulty": {"easy": "Easy", "medium": "Medium", "hard": "Hard", "epic": "Epic"}, "status": {"available": "Available", "completed": "Completed", "claimed": "Claimed", "locked": "Locked"}, "messages": {"rewardClaimed": "<PERSON><PERSON> claimed successfully!", "allTasksComplete": "Congratulations! You've completed all tasks for today!", "streakMilestone": "Amazing! You've reached a {{days}}-day streak milestone!", "newTasksAvailable": "New tasks are now available", "taskCompleted": "Task completed! Click to claim your reward."}, "streakInfo": {"title": "Interaction Streak", "description": "Maintain daily interactions to build your streak and unlock milestone rewards", "currentStreak": "Current Streak", "longestStreak": "Longest Streak", "nextMilestone": "Next Milestone", "milestones": {"3": "3 Day Streak - Basic Reward Package", "7": "7 Day Streak - Weekly Bonus", "15": "15 Day Streak - Special Badge", "30": "30 Day Streak - Premium Rewards", "60": "60 Day Streak - Exclusive Content", "100": "100 Day Streak - Legendary Status", "365": "365 Day Streak - Ultimate Master"}}, "surpriseTask": {"title": "Surprise Moment", "description": "Special limited-time task with bonus rewards", "timeRemaining": "Time Remaining: {{time}}", "bonusReward": "Bonus Reward Available"}}, "journey": {"overview": {"progressToNextLevel": "Progress to Next Level", "totalTrophies": "Total Trophies", "globalRank": "Global Rank", "myRank": "My Rank", "myRankDesc": "Your current standing", "position": "Position", "change": "Change", "topPercentile": "Top Percentile", "journeyRanking": "Journey Ranking", "topPerformers": "Top performers this season", "trophies": "Trophies", "today": "Today", "weeklyGain": "Weekly Gain", "bestStreak": "Best Streak", "dailyGoals": "Daily Goals", "completed": "Completed", "currentStreak": "Current Streak", "onFire": "On Fire", "personalizedRecommendations": "Personalized Recommendations", "basedOnYourActivity": "Based on your activity", "match": "Match", "moreRecommendations": "More Recommendations"}, "progress": {"title": "Progress Rewards", "nextReward": "Next reward at {{count}} tasks", "bigReward": "Big Reward!", "dailyChampion": "Daily Champion", "weeklyMaster": "Weekly Master", "monthlyLegend": "Monthly Legend", "completeAllDaily": "Complete all daily tasks", "completeAllWeekly": "Complete all weekly tasks", "completeAllMonthly": "Complete all monthly tasks", "claim": "<PERSON><PERSON><PERSON>"}, "countdown": {"dailyReset": "Daily Reset", "weeklyReset": "Weekly Reset", "monthlyReset": "Monthly Reset", "tasksResetMidnight": "Tasks reset at midnight", "tasksResetSunday": "Tasks reset every Sunday", "tasksResetMonthEnd": "Tasks reset at month end", "timeUp": "Time's Up!", "tasksResetSoon": "Tasks will reset soon", "urgent": "Urgent: Time running out!", "days": "Days", "hours": "Hours", "minutes": "Min", "seconds": "Sec"}, "levels": {"beginnerJourney": "Beginner Journey", "warmCompanion": "Warm Companion", "deepBond": "Deep Bond", "soulConnection": "Soul Connection", "eternalPath": "Eternal Path", "legendaryVoyage": "Legendary Voyage"}, "tabs": {"overview": "Overview", "overviewDesc": "Level & Rankings", "season": "Season", "seasonDesc": "Trophies & Leaderboard", "monthly": "Monthly", "monthlyDesc": "Daily Rewards", "missions": "Missions", "missionsDesc": "Tasks & Achievements", "daily": "Daily", "weekly": "Weekly"}, "season": {"title": "Season Trophies", "subtitle": "Current Season Progress", "trophiesEarned": "<PERSON><PERSON><PERSON><PERSON> Earned This Season", "daysLeft": "Days Left", "seasonRank": "Season Rank", "milestones": "Season Milestones", "milestonesDesc": "Unlock exclusive rewards", "seasonRanking": "Season Ranking", "topPerformers": "Top performers this season", "trophies": "Trophies", "unlocked": "UNLOCKED", "thisWeek": "this week", "seasonTrophies": "Season Trophies", "leaderboard": "Leaderboard", "viewFullLeaderboard": "View Full Leaderboard"}, "recommendations": {"socialChallenge": "Social Challenge", "socialChallengeDesc": "Connect with 5 new characters today", "newFeature": "New Feature", "newFeatureDesc": "Try the new memory art generator", "easy": "Easy", "medium": "Medium", "hard": "Hard", "premium": "Premium", "trophyReward": "{{amount}} Trophies", "exclusiveBadge": "Exclusive Badge"}, "signIn": {"title": "Monthly Sign-In Reward", "subtitle": "Collect daily rewards and unlock exclusive items", "unlockPass": "Unlock all reward tracks, get ", "unlockDiamond": "Unlock Diamond", "unlockMetaverse": "<PERSON><PERSON>", "freeTrack": "Free Track", "passTrack": "Pass Track", "diamondTrack": "Diamond Track", "metaverseTrack": "MetaVerse Track", "monthlySignIn": "Monthly Sign-In", "consecutiveDays": "consecutive days", "nextSignIn": "Next Sign-In", "monthProgress": "Monthly Progress", "gameSignInOverview": "Gamified Sign-In Overview", "signedDays": "Signed Days", "streak": "Streak", "freeVsPaid": "<PERSON> vs <PERSON><PERSON>", "freeRewards": "Free Rewards", "passRewards": "Pass Rewards", "potentialLoss": "Potential Loss", "upgradeToUnlock": "Upgrade to Alphane Pass", "extraEarnings": "Extra Earnings/Month", "upgradeNow": "Upgrade Now", "freeTrackRewards": "Free Track", "passTrackRewards": "Pass Track", "todayReward": "Today's <PERSON><PERSON>", "yourCurrentPlan": "Your Current Plan", "seeBenefits": "See Benefits", "rewardTracks": "Reward <PERSON>", "rewards": "Rewards"}, "seasonPass": {"unlockSeasonPass": "Unlock {{season}} Pass", "getTripleExp": "Get {{exp}}x Experience", "exclusiveSeasonRewards": "Exclusive {{season}} Rewards", "rewardBoost": "<PERSON><PERSON>", "upgradeNow": "¥30 Upgrade Now", "seasonalEffects": "Seasonal Effects", "exclusiveDecorations": "15+ Exclusive Decorations", "passUsersOnly": "Pass Users Only", "tripleExpBonus": "3x Experience Bonus", "fastUpgrade": "Fast Upgrade", "freePass": "Free Pass", "premiumPass": "Premium Pass", "totalRewards": "Total Rewards", "decorations": "Decorations", "exclusive": "Exclusive", "currentLevel": "Current Level", "nextReward": "Next Reward", "level": "Level", "freeTrack": "Free Track", "premiumTrack": "Premium Track", "activated": "Activated", "upgradeToUnlockAll": "Upgrade to Unlock All Rewards", "upgradeToPremium": "Upgrade to Premium Pass", "unlockExclusiveRewards": "Unlock exclusive rewards and privileges", "unlockAllPremiumRewards": "Unlock all premium rewards", "tripleExperienceBonus": "3x experience bonus", "exclusiveDecorationsAndTitles": "Exclusive decorations and titles", "priorityCustomerSupport": "Priority customer support", "cancel": "Cancel", "buyNow": "¥30 Buy Now", "jumpToPayment": "Redirecting to payment page...", "emptyReward": "Empty Reward", "unlocked": "Unlocked", "seasons": {"winterFantasy": "Winter Fantasy", "springAwakening": "Spring Awakening", "summerCarnival": "Summer Carnival", "autumnTales": "Autumn Tales", "winterDescription": "In the dreamy snow, spend warm time with characters", "springDescription": "Vibrant season, explore new stories and possibilities", "summerDescription": "Passionate summer, create unforgettable memories with friends", "autumnDescription": "Golden autumn, harvest the fruits of growth and wisdom", "snowEffects": "Snow Effects", "winterExclusiveDecorations": "Winter Exclusive Decorations", "festivalLimitedRewards": "Festival Limited Rewards", "petalFalling": "Petal Falling", "springGrowthBonus": "Spring Growth Bonus", "newCharacterUnlock": "New Character Unlock", "sunlightEffects": "Sunlight Effects", "summerEvents": "Summer Events", "doubleExperience": "Double Experience", "fallingLeavesAnimation": "Falling Leaves Animation", "harvestFestival": "Harvest Festival", "thanksgivingThemeRewards": "Thanksgiving Theme Rewards"}, "rewards": {"coins": "Coins", "diamonds": "Diamonds", "expAccelerator": "Experience Accelerator", "starryAvatarFrame": "Starry Avatar <PERSON>", "legendaryTitle": "Legendary Title", "luckyCharm": "Lucky Charm", "rainbowChatBubble": "Rainbow Chat Bubble", "doubleExpCard": "Double Experience Card", "exclusiveBackgroundTheme": "Exclusive Background Theme", "ultimateGloryBadge": "Ultimate Glory Badge"}}, "membership": {"monthly": "Monthly", "yearly": "Yearly", "freeVersion": "Alphane Explorer", "passMember": "Alphane Pass", "diamondMember": "Alphane Diamond", "infinityMember": "Alphane Infinity", "basicChatFeatures": "Basic chat features", "dailyAiInteractions": "5 daily AI interactions", "communityAccess": "Community access", "unlimitedAiChat": "Unlimited AI chat", "prioritySupport": "Priority customer support", "advancedAnalytics": "Advanced analytics", "customThemes": "Custom themes", "doubleExperience": "Double experience points", "passExclusiveBadge": "Alphane Pass exclusive badge", "allPassFeatures": "All Alphane Pass features included", "advancedCharacterSlots": "Advanced character slots", "exclusiveContentAccess": "Exclusive content access", "earlyAccessFeatures": "Early access to new features", "tripleExperience": "Triple experience points", "diamondExclusiveAvatar": "Diamond exclusive avatar", "monthlyRewardPackage": "Monthly reward package", "allDiamondFeatures": "All Diamond features included", "metaverseExclusiveContent": "Metaverse exclusive content", "unlimitedCharacterSlots": "Unlimited character slots", "metaverseExclusiveAvatar": "Metaverse exclusive avatar", "quarterlyRewardPackage": "Quarterly reward package", "currentPlan": "Current Plan", "upgradeTo": "Upgrade to", "save": "Save", "upgradeNow": "Upgrade Now", "close": "Close"}, "missions": {"title": "Missions", "subtitle": "Complete missions to earn experience and advance your journey", "daily": "Daily Missions", "weekly": "Weekly Missions", "seasonal": "Seasonal Missions", "viewAllTasks": "View All Tasks", "missionProgress": "{{completed}}/{{total}} Completed", "allTasksIntegrated": "All Tasks Integrated", "allCompleted": "All missions completed!", "checkBack": "Check back tomorrow for new {{type}} missions.", "dailyChat": {"title": "Morning Conversation", "description": "Have a meaningful conversation with any AI character"}, "dailyMemory": {"title": "Memory Keeper", "description": "Save a new memory to your memory capsule"}, "dailyExplore": {"title": "Character Explorer", "description": "Discover and chat with 2 different characters"}, "weeklyBond": {"title": "Deepening Bonds", "description": "Increase intimacy level with any character"}, "weeklyCreate": {"title": "Creative Soul", "description": "Create or edit a character this week"}, "weeklyMemories": {"title": "Memory Collector", "description": "Save 10 meaningful memories"}, "seasonalLegend": {"title": "Legendary Companion", "description": "Maintain a 30-day interaction streak"}, "seasonalCreator": {"title": "Master Creator", "description": "Create 5 characters that receive community likes"}}, "categories": {"interaction": "Interaction", "creation": "Creation", "social": "Social", "memory": "Memory"}, "completed": "Completed", "rewards": {"currency": "<PERSON><PERSON><PERSON><PERSON>", "cosmetics": "Cosmetics", "items": "Items", "exclusive": "Exclusive Content", "reward": "<PERSON><PERSON>", "rarity": {"common": "Warm", "rare": "Precious", "epic": "Heart Joy", "legendary": "Legendary"}}, "social": {"ranking": "Journey Ranking", "myRank": "My Rank: #{{rank}}", "friendsProgress": "Friends Progress", "shareProgress": "Share Progress", "topTravelers": "Top Travelers"}, "purchase": {"heartTrack": "Unlock Heart Track", "diamondTrack": "Unlock Diamond Track", "heartPrice": "$9.99", "diamondPrice": "$19.99", "benefits": "Benefits include:", "exclusiveRewards": "Exclusive premium rewards", "fastTrack": "50% bonus experience", "earlyAccess": "Early access to new content"}, "messages": {"rewardClaimed": "<PERSON><PERSON> successfully claimed!", "levelUp": "Congratulations! You've reached Level {{level}}!", "trackUnlocked": "{{track}} Track has been unlocked!", "seasonComplete": "Amazing! You've completed this season's journey!", "milestoneReward": "Milestone reward claimed! You received: {{amount}} Star Diamonds", "missionCompleted": "Mission completed! You can now claim your reward.", "afterReset": "after reset"}}, "mindFuel": {"title": "Mind Fuel System", "currentMindFuel": "Current Mind Fuel", "maxMindFuel": "Max Mind Fuel", "nextRecovery": "Next Recovery", "recoveryTime": "Recovery Time", "unlimitedMindFuel": "Unlimited Mind Fuel", "mindFuelFull": "Mind Fuel Full", "aboutToRecover": "About to Recover", "quickRecharge": "Quick Recharge", "useItem": "Use Mind Supply", "goToStore": "Go to Store", "sidebar": {"title": "Mind Fuel", "nextRecovery": "Next Recovery", "unlimited": "Unlimited", "full": "Full", "aboutToRecover": "About to Recover"}, "recharge": {"title": "Mind Fuel Recharge", "currentStatus": "Current Status", "rechargeOptions": "Recharge Options", "confirm": "Confirm Recharge", "cancel": "Cancel", "success": "Recharge successful! Consumed {{cost}} {{currency}}", "options": {"mindFuel1": "1 Mind Fuel", "mindFuel5": "5 Mind Fuel", "fullRestore": "Full Restore", "alphaneDescription": "Recharge with Alphane Dust", "endoraDescription": "Recharge with Endora Crystal", "fullDescription": "Restore to full mind fuel"}}, "items": {"title": "Use Mind Supply", "currentStatus": "Current Status", "noItems": "No mind supply items available", "goToBuy": "Go to store to buy more items", "use": "Use", "cannotUse": "Cannot Use", "alreadyFull": "Full", "confirmUse": "Use {{itemName}}", "quantity": "Quantity", "confirm": "Confirm Use", "cancel": "Cancel", "success": "Successfully used {{quantity}} {{itemName}}!", "types": {"mindSupplySmall": "Small Mind Supply", "mindSupplyStandard": "Standard Mind Supply", "mindSupplyPremium": "Premium Mind Supply", "mindSupplyPerfect": "Perfect Mind Supply", "restoreDescription": "Restore {{amount}} mind fuel", "fullRestoreDescription": "Fully restore mind fuel"}, "rarity": {"common": "Common", "rare": "Rare", "epic": "Epic", "legendary": "Legendary"}, "inventory": {"owned": "Owned: {{count}}", "canUse": "Can use {{count}}"}}, "membership": {"currentTier": "Current Tier", "upgrade": "Upgrade Membership", "upgradeNow": "Upgrade Now", "benefits": "Member Benefits", "mindFuelLimit": "Mind Fuel Limit", "recoverySpeed": "Recovery Speed", "tiers": {"standard": "Alphane Explorer", "pass": "Alphane Pass", "diamond": "Alphane Diamond", "infinity": "Alphane Infinity"}, "features": {"basicChat": "Basic chat features", "dailyAI": "Daily AI interactions", "communityAccess": "Community access", "unlimitedChat": "Unlimited conversations", "prioritySupport": "Priority support", "basicAnalysis": "Basic analysis", "doubleMindFuel": "2x mind fuel limit", "exclusiveAvatar": "Exclusive avatar frames", "memberBadge": "Member badge", "diamondPrivilege": "Diamond privileges", "advancedFeatures": "Advanced features", "exclusiveContent": "Exclusive content", "priorityQueue": "Priority queue", "quintupleMindFuel": "5x mind fuel limit", "diamondBadge": "Diamond badge", "exclusiveEffects": "Exclusive effects", "advancedAnalysis": "Advanced analysis", "metaverseExperience": "Metaverse experience", "allFeatures": "All features", "personalAdvisor": "Personal advisor", "limitedContent": "Limited content", "highestPriority": "Highest priority", "unlimitedMindFuel": "Unlimited mind fuel", "metaverseBadge": "Metaverse badge", "exclusiveAnimations": "Exclusive animations", "personalConsultant": "Personal consultant", "vipAccess": "VIP access"}, "recoveryTimes": {"perHour": "1x", "per30Min": "3x", "per12Min": "10x", "noWait": "∞"}, "comparison": {"mindFuelLimit": "Mind Fuel Limit:", "recoverySpeed": "Recovery Speed:", "upgradePrompt": "🚀 Upgrade membership, double your mind fuel!", "hot": "Hot", "recommended": "Recommended", "discount": "-{{percent}}%", "originalPrice": "${{price}}/month", "currentPrice": "${{price}}/month", "upgradeToTier": "Upgrade to {{tierName}}", "maxTierReached": "🎉 You're already at the highest tier", "maxTierDescription": "Enjoy unlimited mind fuel and all exclusive privileges", "viewAllTiers": "View all membership tiers", "membershipDetails": "Membership Tier Details", "month": "/month", "perMonth": "/month"}}}, "streak": {"milestonemultiplier": "Milestone gifts multiplier: {{multiplier}}"}, "tokens": {"alphane": "Glimmering Dust", "endora": "<PERSON>", "serotile": "Memory Puzzle", "oxytol": "<PERSON>", "currency": "<PERSON><PERSON><PERSON><PERSON>"}, "subscription": {"title": "Subscription", "subtitle": "100% off - Limited Time Offer!", "monthly": "Monthly", "annually": "Annually", "discount25": "-25%", "currentPlan": {"title": "Current Plan: Alphane Pass", "nextBilling": "Next billing: December 15, 2024 • $9.99/month", "manageBilling": "Manage Billing", "downloadInvoice": "Download Invoice"}, "plans": {"pass": {"name": "Alphane Pass", "features": ["Daily limited Fast Request privilege", "Daily login rewards: 50 Glimmering Dust + 10 Joy Crystals", "Unlock Battle Pass advanced reward track", "1-2 Streak Freeze cards per month", "Create and share public character cards", "Exclusive pass member identity badge", "Gift 3 trial passes to friends monthly", "Unlimited Slow Request"]}, "diamond": {"name": "Alphane Diamond", "features": ["Unlimited Fast Request privilege", "Enhanced daily rewards: 150 Dust + 50 Crystals + 1 Memory Puzzle", "Journey diamond collection track access", "5 Streak Freeze cards + 2 free streak repairs monthly", "Creator incentive program (50% revenue share)", "Exclusive Whisper Space access", "Enhanced AI Memory Capsule capacity (3x)", "Priority access to new AI models & features", "Exclusive diamond member privileges", "Premium character creation tools"]}}, "buttons": {"cancel": "Cancel Plan", "current": "Current Plan", "upgrade": "Upgrade", "popular": "Most Popular"}, "periods": {"forever": "forever", "month": "month", "year": "year"}, "seats": {"one": "1 seat", "three": "3 seats", "many": "300 seats"}, "additionalInfo": {"description": "Upgrade to premium plans to unlock more amazing features. Multiple payment methods supported, cancel anytime.", "refund": "7-day money-back guarantee", "security": "Secure payment protection", "support": "24/7 customer support", "cancel": "Cancel anytime"}}, "chat": {"status": {"online": "Online"}, "input": {"placeholder": "Message...", "sendMessage": "Send Message", "switchToVoiceMode": "Switch to Voice Mode", "switchToTextMode": "Switch to Text Mode", "moreActions": "More Actions", "recording": "🎤 Recording... Tap to stop"}, "actions": {"gallery": "Gallery", "camera": "Camera", "voiceCall": "Voice Call", "shareMoment": "Share Moment", "shareCharacter": "Share Character", "shareMemory": "Share Memory"}, "suggestions": {"conversationStarters": "Conversation starters", "refreshSuggestions": "Refresh suggestions"}}, "contact": {"title": "Contact Us", "subtitle": "We cherish every message from you.", "form": {"contactType": {"label": "Contact Type", "options": {"wishlist": "Wishlist", "reportBug": "Report Bug", "reportAbuse": "Report Abuse", "suggestions": "Suggestions", "join": "Join", "invest": "Invest", "other": "Other"}}, "email": {"label": "Email (Optional)", "placeholder": "Enter your email address to receive replies", "required": "Email is required if you want to receive replies", "invalid": "Please enter a valid email address"}, "title": {"label": "Title", "placeholder": "Enter your message title", "required": "Title is required"}, "content": {"label": "Content (Max 1000 characters)", "placeholder": "Enter your message content...", "required": "Content is required", "maxLength": "Content must not exceed 1000 characters"}, "characterCount": "{{count}}/1000 characters", "submit": "Send Message", "submitting": "Sending...", "validation": {"titleRequired": "Please enter a title", "contentRequired": "Please enter your message content", "contentTooLong": "Content exceeds maximum length"}}, "success": {"title": "Message Sent!", "message": "Thank you for contacting us. We'll get back to you as soon as possible.", "sendAnother": "Send Another Message", "backToHome": "Back to Home"}, "error": {"title": "Failed to Send", "message": "Sorry, there was an error sending your message. Please try again later.", "tryAgain": "Try Again"}, "additionalInfo": "We typically respond within 24 hours. For urgent matters, please mark your message as \"Report Bug\" or \"Report Abuse\".", "emailContact": {"title": "Or contact us directly", "email": "<EMAIL>", "copyEmail": "Copy", "emailCopied": "Copied!"}}, "characterEdit": {"title": "Edit Character", "subtitle": "Modify your character's details and settings", "saveChanges": "Save Changes", "savingChanges": "Saving Changes...", "characterUpdated": "Character updated successfully", "navigation": {"previous": "Previous", "next": "Next", "finish": "Save Changes", "stepOf": "Step {{current}} of {{total}}", "stepComplete": "Step completed", "stepIncomplete": "Please complete required fields"}}, "characterCreation": {"title": "Create Character", "subtitle": "Bring your imagination to life with AI-powered character creation", "functions": {"flash": {"title": "Flash", "description": "Quick character generation in 60 seconds", "subtitle": "Build Character in 60 seconds"}, "customize": {"title": "Customize", "description": "Detailed character customization with full control"}, "import": {"title": "Import", "description": "Import from SillyTavern cards or external files"}}, "steps": {"basics": {"title": "Basics", "description": "Name, Gender, Image, Appearance, Personality, Tags"}, "personality": {"title": "Behavior & Knowledge", "description": "Behavior, Knowledge, Background, Visibility"}, "advanced": {"title": "Advanced", "description": "Quick Start or Advanced Story Chapters"}}, "flash": {"characterName": "Character Name", "characterConcept": "Character Concept", "namePlaceholder": "Enter character name (optional - AI will generate if empty)", "conceptPlaceholder": "Describe your character: Appearance, personality, behaviour, knowledge, and background story. We will generate remaining details for you...", "generateButton": "🚀 Generate Character", "generatingText": "Generating Magic...", "aiFeatures": "✨ AI will automatically generate these aspects:", "previewTitle": "✨ Generated Character Preview", "regenerationsLeft": "Regenerations left:", "nameTag": "✨ Name", "appearanceTag": "👤 Appearance", "personalityTag": "🎭 Personality", "behaviorTag": "🎪 Behavior", "knowledgeTag": "🧠 Knowledge", "storyTag": "📖 Story"}, "filters": {"gender": "Gender", "pointOfView": "Point of View", "any": "Any", "female": "Female", "male": "Male", "other": "Other", "femalePOV": "Female POV", "malePOV": "Male POV", "otherPOV": "Other POV"}, "import": {"title": "Import Character", "subtitle": "Upload SillyTavern cards or script files to create characters", "sillyTavern": "📄 SillyTavern Character Cards", "uploadArea": {"title": "Click or drag to upload", "subtitle": "Support SillyTavern .json files", "button": "📄 Choose JSON files", "fileSelected": "File selected, click to choose another", "readyToImport": "✅ Ready to import"}, "features": {"title": "📋 Import Features", "characterCards": "📄 Character Cards", "autoExtract": "Auto-extract character data", "fillDescription": "Fill description & background"}, "selectCharacter": {"title": "🎯 Select Character", "subtitle": "🎭 Select Character to Import ({count} found)"}, "scriptFiles": {"title": "📝 Script Files", "uploaded": "<PERSON><PERSON><PERSON> uploaded successfully", "uploadTitle": "Upload Script File", "uploadSubtitle": "Drag & drop or click to upload .txt file (max 16KB)"}, "importFeatures": {"title": "📋 Import Features", "characterCards": "📄 Character Cards", "multipleFiles": "Multiple .json files at once", "singleFileMultiple": "Single .json with multiple characters", "sillyTavernSupport": "SillyTavern format support", "txtSupport": ".txt file support (max 16KB)", "autoExtract": "Auto-extract character data", "fillBackground": "Fill description & background"}, "filters": {"gender": "Gender", "pointOfView": "Point of View", "any": "Any", "female": "Female", "male": "Male", "other": "Other", "femalePOV": "FemalePOV", "malePOV": "MalePOV", "otherPOV": "OtherPOV"}}, "basics": {"title": "Character Basics", "subtitle": "Set up your character's fundamental information", "characterName": "Character Name", "namePlaceholder": "Enter character name...", "nameOriginPlaceholder": "Enter the origin or meaning of the name (optional)", "genderPov": "Gender & POV", "appearance": "Appearance", "appearancePlaceholder": "Physique and facial features, clothings and adornments...", "personality": "Personality", "traits": "Trai<PERSON>", "traitsPlaceholder": "Describe character traits: brave, kind, stubborn, curious, loyal, mischievous, perfectionist, adventurous, introverted, optimistic...", "mind": "Mind", "mindPlaceholder": "Describe thinking patterns: analytical, creative, logical, intuitive, strategic, impulsive, methodical, abstract, practical, philosophical...", "emotion": "Emotion", "emotionPlaceholder": "Describe emotional patterns: empathetic, passionate, calm, sensitive, expressive, reserved, warm, intense, gentle, dramatic, stable...", "characterSettings": "Character Settings", "characterSettingsPlaceholder": "Describe the character's background settings, status, occupation, or special circumstances...", "additionalTags": "Additional Tags", "tagPlaceholder": "Enter a tag (e.g., friendly, mysterious, warrior)", "addTag": "Add Tag"}, "personality": {"title": "Behavior & Knowledge", "subtitle": "Define your character's behavior patterns and knowledge base", "behaviour": "Behaviour", "defaultGreeting": "Default Greeting Message", "greetingPlaceholder": "Enter the first message your character will say when users start chatting...", "speechStyle": "Speech Style", "speechStylePlaceholder": "Describe your character's speaking style and tone...", "facialExpressions": "Facial Expressions", "facialExpressionsPlaceholder": "Describe your character's facial expressions and habits...", "bodyLanguage": "Body Language", "bodyLanguagePlaceholder": "Describe your character's body language and gestures...", "knowledge": "Knowledge", "knowledgeBase": "Knowledge Base", "knowledgePlaceholder": "Enter your character's general knowledge, facts, or background information they should know about...", "uploadKnowledgeFiles": "Upload Knowledge Files", "uploadKnowledgeDesc": "Support .json, .md, .txt format files", "uploadKnowledgeButton": "Click to upload knowledge files", "uploadedFiles": "Uploaded files:", "deleteFile": "Delete file", "goodAt": "Good At", "goodAtPlaceholder": "e.g., cooking, mathematics, sword fighting, magic spells, leadership...", "badAt": "Bad At", "badAtPlaceholder": "e.g., lying, technology, swimming, public speaking, remembering names...", "add": "Add", "backgroundStory": "Background Story", "relationshipWithPlayer": "Relationship with Player", "relationshipPlaceholder": "Describe the character's relationship background with the player...", "importantExperiences": "Important Past Experiences", "experiencesPlaceholder": "Describe the character's important experiences and background story...", "visibility": "Visibility", "public": "Public", "publicDesc": "Everyone can see and chat", "unlisted": "Unlisted", "unlistedDesc": "Only with direct link", "private": "Private", "privateDesc": "Only you can access"}, "advanced": {"title": "Advanced", "subtitle": "Quick Start or Advanced Story Chapters", "readyToChat": "Ready to Start Chatting?", "readyToChatDesc": "Your character is ready! Click below to begin your conversation.", "readyToChatButton": "Ready to Chat Now", "advancedStoryBuilding": "Advanced Story Building", "advancedStoryDesc": "Create detailed story chapters and customize advanced settings.", "checkPassButton": "Check Pass & Advanced Settings", "passVerified": "Pass Verified", "passVerifiedDesc": "Active Pass or Diamond Pass detected. You can now build detailed story chapters and use advanced features.", "noActivePass": "No Active Pass", "noActivePassDesc": "Advanced features require an active Pass. Please upgrade to access story building tools.", "uploadScript": "Upload Script for Plot Analysis", "uploadScriptButton": "Upload Script File", "uploadScriptDesc": "Supports .txt, .md, .json format script files", "storyChapters": "Story Chapters & Character Greetings", "updatedGreeting": "Updated Greeting", "greetingAfterChapter": "Greeting After Chapter Completion", "greetingAfterPlaceholder": "Enter new greeting after completing specific chapter...", "triggerAfterChapter": "Trigger After Chapter", "chapter": "Chapter", "addChapter": "Add Chapter", "addGreeting": "Add Greeting"}, "imageUpload": {"characterImages": "Character Images", "characterPortrait": "Character Portrait", "avatarAutoCropped": "Avatar (Auto-cropped)", "uploadCharacterImage": "Upload Character Image", "clickOrDragToUpload": "Click or drag to upload", "characterImageUploaded": "Character Image Uploaded", "clickToChangeImage": "Click to change image", "avatarReady": "Avatar Ready", "croppedFromCharacterImage": "Cropped from character image", "uploadCharacterImageFirst": "Upload character image first", "avatarWillBeAutoCropped": "Avatar will be auto-cropped", "avatarPreview": "Avatar Preview:", "avatarSetupComplete": "✅ Avatar setup complete", "sourceCroppedFromCharacterImage": "Source: Cropped from character image"}, "cropper": {"avatar": {"title": "Crop Avatar", "character": "Crop Character Image", "description": "Drag the selection box to adjust {{type}} area, ratio: {{ratio}}", "selectAspectRatio": "Select Aspect Ratio", "avatarPreview": "Avatar Preview", "characterPreview": "Character Preview", "avatarInfo": "Avatar Information", "characterRatio": "Character Ratio", "dragToMove": "Drag to move crop area", "dragCorners": "Drag corners to resize", "mouseWheel": "Mouse wheel to zoom crop area", "resetArea": "Reset Area", "resizing": "Resizing", "dragToMoveText": "Drag to move", "cancel": "Cancel", "confirmCrop": "Confirm Crop", "ratios": {"5:6": "Classic Portrait", "5:8": "Full Body Portrait"}}}, "personalityTags": {"addCustomTag": "Add Custom Tag", "customTagPlaceholder": "Enter personality trait...", "addButton": "Add", "addTagHint": "Press Enter or click Add button to add custom tag", "presetTags": {"gentle": "Gentle", "lively": "Lively", "mysterious": "Mysterious", "calm": "Calm", "humorous": "Humorous", "serious": "Serious", "innocent": "<PERSON>", "mature": "Mature", "cheerful": "Cheerful", "introverted": "Introverted", "curious": "Curious", "kind": "Kind", "brave": "Brave", "wise": "<PERSON>", "strong": "Strong", "elegant": "Elegant", "cute": "Cute", "stern": "Stern", "optimistic": "Optimistic", "pessimistic": "Pessimistic", "romantic": "Romantic", "realistic": "Realistic", "rational": "Rational", "emotional": "Emotional"}}, "navigation": {"previous": "Previous", "next": "Next", "stepOf": "Step {{current}} of {{total}}", "finish": "Create Character", "stepComplete": "This step is complete", "stepIncomplete": "Please complete all required fields to continue"}}, "storyCreation": {"pageTitle": "Create Story - <PERSON><PERSON>", "pageDescription": "Create detailed story plots and chapters for your character to enhance interactive experiences", "title": "Create Your Story", "subtitle": "Craft an engaging narrative experience", "selectCharacter": {"title": "Create Your Story", "subtitle": "Choose a character to begin your adventure", "searchPlaceholder": "Search characters by name or description...", "selectYourCharacter": "Select Your Character", "viewMode": "View:", "filters": "Filters", "storyStatus": "Story Status", "allCharacters": "All Characters", "withStories": "With Stories", "withoutStories": "Without Stories", "gender": "Gender", "allGenders": "All Genders", "male": "Male", "female": "Female", "other": "Other", "pointOfView": "Point of View", "allPOV": "All POV", "firstPerson": "First Person", "secondPerson": "Second Person", "thirdPerson": "Third Person", "filterByTags": "Filter by Tags", "addTagPlaceholder": "Add tag to filter...", "add": "Add", "charactersFound": "{{count}} characters found", "characterFound": "{{count}} character found", "sortBy": "Sort by:", "name": "Name", "fans": "Fans", "heatScore": "Heat Score", "trend": "Trend", "lastUpdated": "Last Updated", "totalStories": "Total Stories", "totalLikes": "Total Likes", "totalPlays": "Total Plays", "noMatchingCharacters": "No matching characters", "noMatchingCharactersDesc": "Try adjusting your search or filter criteria to find the perfect character for your story.", "clearAllFilters": "Clear All Filters", "noCharactersYet": "No characters yet", "noCharactersYetDesc": "Create your first AI character to start building amazing stories and experiences.", "createFirstCharacter": "Create Your First Character", "noDescriptionAvailable": "No description available", "createStory": "Create Story"}, "success": {"storyCreated": "Story created successfully!"}, "error": {"failedToGenerate": "Failed to generate story, please try again", "failedToCreate": "Failed to create story, please try again", "characterNotFound": "Character Not Found", "characterNotFoundDescription": "The character you're looking for doesn't exist or has been removed.", "backToCharacterSelection": "Back to Character Selection"}, "functions": {"flash": {"title": "Flash", "description": "Quick story generation with AI", "placeholder": "Describe your story concept in one sentence...", "generate": "Generate Story", "generating": "Generating story...", "example": "Example: A romantic adventure in ancient Rome", "needInspiration": "Need inspiration? Try these examples:", "helpText": "💡 AI will generate a complete story with scene settings, character psychology, and interaction dynamics"}, "customize": {"title": "Customize", "description": "Detailed story customization", "steps": {"basics": "Story & Cover", "details": "Setting & Theme", "worldSetting": "World Setting", "storyFlow": "Story Flow", "objectivesSubjectives": "Objectives & Subjectives"}}}, "worldSetting": {"title": "World Setting", "description": "Configure the world and environment settings for your story", "basicInfo": {"title": "Basic Information", "storyName": "Story Name", "storyNamePlaceholder": "Enter your story's name...", "storyDescription": "Story Description", "storyDescriptionPlaceholder": "Provide a brief description of your story for external display...", "openingMessage": "Opening Message", "openingMessagePlaceholder": "Enter the opening message that will be shown to users when they start this story...", "storyTags": "Story Tags", "tagPlaceholder": "Enter a tag (e.g., romance, adventure, mystery)", "addTag": "Add Tag", "removeTag": "Remove", "coverImage": "Cover Image", "uploadCoverImage": "Upload Cover Image", "coverImageUploaded": "Cover image uploaded", "clickToChangeImage": "Click to change image", "clickOrDragToUpload": "Click or drag to upload"}, "quickSetup": {"title": "Quick Setup", "worldOverview": "World Overview", "worldOverviewPlaceholder": "Describe the overall setting of your story world...", "storyBackground": "Story Background", "storyBackgroundPlaceholder": "Describe the background context of your story..."}, "basicSettings": {"title": "Basic Settings", "historicalEra": "Historical Era", "historicalEraPlaceholder": "e.g., Victorian Era, Golden Twenties, 2077, Star Exploration Era...", "geographicEnvironment": "Geographic Environment", "geographicEnvironmentPlaceholder": "e.g., Mega City, Border Desert, Underwater City, Floating Islands...", "mainRaces": "Main Races", "mainRacesPlaceholder": "Describe the main races and civilizations in the world...", "coreConflict": "Core Conflict", "coreConflictPlaceholder": "Select core conflict...", "storyMainline": "Story Mainline", "storyMainlinePlaceholder": "Describe the main storyline and plot direction...", "storyMainlinePlaceholderNew": "e.g., Save the sealed goddess, stop a world-ending conspiracy, find lost memory fragments, rebuild a shattered kingdom, resolve millennia-old racial hatred...", "coreObjective": "Core Mission Objective", "coreConflictOptions": {"classOpposition": "Class Opposition", "ideologicalStruggle": "Ideological Struggle", "racialWar": "Racial War", "humanNatureConflict": "Human vs Nature", "resourceScarcity": "Resource Scarcity"}}, "advancedSettings": {"title": "Advanced Settings", "optional": "Optional, make your world richer", "coreWorldRules": "Core World Rules", "socialEconomicSettings": "Social & Economic Settings", "socialPoliticalSystem": "Social Political System", "socialFormPlusPolitical": "Social Form + Political Structure", "socialPoliticalSystemPlaceholder": "e.g., Post-capitalist federal republic, digital nomad alliance combining anarchist autonomy with AI-assisted democratic decision-making, voting weight based on algorithmic contribution...", "customOption": "Custom...", "physicsRulesCustomPlaceholder": "Describe your custom physics rules, e.g., gravity controlled by thought, time flows backward in certain areas...", "techLevelCustomPlaceholder": "Describe your custom tech level, e.g., biotechnology highly advanced but electronics stagnant...", "supernaturalElementsNewPlaceholder": "e.g., High magic coexists with steampunk technology, ancient gods slumber but influence world operations, souls can be transferred to mechanical vessels through special rituals...", "timeBackgroundNewPlaceholder": "e.g., Reconstruction period 50 years after World War III, conflict between emerging technology and traditional values reaches its peak...", "economicFoundationNewPlaceholder": "e.g., Time-based currency economic system, intellectual property as primary wealth, robot labor eliminates traditional employment concepts...", "showAdvanced": "Show Advanced Settings", "hideAdvanced": "Hide Advanced Settings", "worldBackgroundSettings": "World Background Settings", "physicsAndRulesSettings": "Physics & Rules Settings", "physicsRules": "Physics Rules", "physicsRulesPlaceholder": "Select physics rules...", "physicsRulesOptions": {"realistic": "Realistic Physics", "softScifi": "Soft Sci-Fi Physics", "highFantasy": "High Fantasy Magic", "cosmicHorror": "Cosmic Horror Unknown"}, "supernaturalElements": "Supernatural Elements", "supernaturalElementsPlaceholder": "Select supernatural elements...", "supernaturalElementsOptions": {"magicExists": "Magic Exists", "godsExist": "Gods Exist", "otherworldlyBeings": "Otherworldly Beings", "soulReincarnation": "Soul Reincarnation", "noSupernatural": "No Supernatural Forces"}, "socialForm": "Social Form", "socialFormPlaceholder": "Select social form...", "socialFormOptions": {"capitalism": "Capitalism", "postCapitalism": "Post-Capitalism", "cyberpunkFeudalism": "Cyberpunk Feudalism", "tribalAlliance": "Tribal Alliance", "anarchistFederation": "Anarchist Federation"}, "politicalStructure": "Political Structure", "politicalStructurePlaceholder": "Select political structure...", "politicalStructureOptions": {"federation": "Federation", "empire": "Empire", "republic": "Republic", "monarchy": "Monarchy", "theocracy": "Theocracy"}, "economicFoundation": "Economic Foundation", "economicFoundationPlaceholder": "Describe the economic system...", "techLevel": "Technology Level", "techLevelPlaceholder": "Select tech level...", "techLevelOptions": {"industrialRevolution": "Industrial Revolution", "informationAge": "Information Age", "cyberpunkNearFuture": "Cyberpunk Near Future", "interstellarCivilization": "Interstellar Civilization", "magicTechFusion": "Magic-Tech Fusion"}, "timeBackground": "Time Background", "timeBackgroundPlaceholder": "Select time background...", "timeBackgroundOptions": {"postWarReconstruction": "Post-War Reconstruction", "techExplosionEve": "Tech Explosion Eve", "doomsdayCountdown": "Doomsday Countdown", "goldenAge": "Golden Age", "greatDepression": "Great Depression"}}}, "storyFlow": {"title": "Story Flow", "description": "Create and organize story chapters", "chapterTitlePlaceholder": "Enter chapter title", "chapterDescriptionPlaceholder": "Brief description of this chapter", "chapterContentPlaceholder": "Chapter content and narrative", "chapters": {"title": "Chapter Management", "addMainChapter": "Add Main Chapter", "addBranchChapter": "Add Branch Chapter", "deleteChapter": "Delete Chapter", "removeChapter": "Remove Chapter", "chapterTitle": "Chapter Title", "chapterDescription": "Chapter Description", "chapterContent": "Chapter Content", "backgroundSetting": "Background Setting", "backgroundSettingPlaceholder": "Describe the setting and atmosphere for this chapter...", "backgroundImage": "Background Image", "uploadBackgroundImage": "Upload Background Image", "backgroundImageUploaded": "Background image uploaded", "clickToChangeImage": "Click to change image", "clickOrDragToUpload": "Click or drag to upload", "useWorldSettingImage": "Use world setting image", "usingWorldSettingImage": "Using world setting image", "inheritedFromWorldSetting": "Inherited from world setting", "completionEffects": "Completion Effects", "bondPointsChange": "Bond Points Change", "bondPointsPlaceholder": "Enter bond points change (e.g., +10, -5, 0)", "greetingChange": "New Greeting Message", "greetingChangePlaceholder": "New greeting after completing this chapter...", "characterMoodChange": "Character Mood Change", "characterMoodChangePlaceholder": "How does the character's mood change?", "customEffects": "Other Effects", "customEffectsPlaceholder": "Any other effects or changes...", "mainChapter": "Main Chapter", "branchChapter": "Branch Chapter", "noChapters": "No chapters yet", "createFirstChapter": "Create first chapter", "addFirstChapter": "Add first chapter", "selectChapter": "Select a Chapter", "selectChapterDescription": "Choose a chapter from the story flow to edit its details", "choices": "Chapter Choices", "addChoice": "Add Choice", "choiceText": "Choice Text", "choiceDescription": "Choice Description", "choiceTriggerCondition": "Trigger Condition", "choiceTriggerConditionPlaceholder": "e.g., Bond level ≥ 3, Has completed previous task...", "nextChapter": "Next Chapter", "selectNextChapter": "Select next chapter...", "flowPreview": "Flow Preview"}, "navigation": {"previous": "Previous", "next": "Next", "continueToObjectives": "Continue to Objectives", "backToWorldSetting": "Back to World Setting"}}, "steps": {"worldSetting": {"title": "World Setting", "description": "Define your story world's foundation"}, "storyFlow": {"title": "Story Flow", "description": "Create and organize story chapters", "chapterTitle": "Chapter Title", "chapterTitlePlaceholder": "Enter chapter title", "chapterDescription": "Chapter Description", "chapterDescriptionPlaceholder": "Brief description of this chapter", "chapterContent": "Chapter Content", "chapterContentPlaceholder": "Chapter content and narrative", "backgroundSetting": "Background Setting", "backgroundSettingPlaceholder": "Describe the setting and atmosphere for this chapter...", "backgroundImage": "Background Image", "uploadBackgroundImage": "Upload Background Image", "backgroundImageUploaded": "Background image uploaded", "clickToChangeImage": "Click to change image", "clickOrDragToUpload": "Click or drag to upload", "useWorldSettingImage": "Use world setting image", "usingWorldSettingImage": "Using world setting image", "inheritedFromWorldSetting": "Inherited from world setting", "completionEffects": "Completion Effects", "bondPointsChange": "Bond Points Change", "greetingChange": "New Greeting Message", "greetingChangePlaceholder": "New greeting after completing this chapter...", "characterMoodChange": "Character Mood Change", "characterMoodChangePlaceholder": "How does the character's mood change?", "customEffects": "Other Effects", "customEffectsPlaceholder": "Any other effects or changes...", "selectChapter": "Select a Chapter", "selectChapterDescription": "Choose a chapter from the story flow to edit its details", "storyFlow": "Story Flow"}, "objectivesSubjectives": {"title": "Objectives & Subjectives", "description": "Configure chapter objectives and character psychology", "selectChapter": "Select a Chapter", "selectChapterDescription": "Choose a chapter from the story flow to configure its objectives and subjectives", "navigation": {"backToStoryFlow": "Back to Story Flow"}}, "objectives": {"title": "Objective Elements", "scene": {"title": "Scene Layer", "timeElements": "Time Elements", "season": "Season", "selectSeason": "Select season...", "seasons": {"spring": "Spring", "summer": "Summer", "autumn": "Autumn", "winter": "Winter"}, "timeOfDay": "Time of Day", "selectTimeOfDay": "Select time of day...", "timesOfDay": {"dawn": "Dawn", "morning": "Morning", "noon": "<PERSON>on", "afternoon": "Afternoon", "evening": "Evening", "night": "Night", "midnight": "Midnight"}, "duration": "Duration", "durationPlaceholder": "e.g., 30 minutes, 2 hours, all day...", "specialDate": "Special Date", "specialDatePlaceholder": "If there's a date with special meaning...", "spatialElements": "Spatial Elements", "location": "Location", "locationPlaceholder": "Describe the specific location...", "atmosphere": "Atmosphere", "atmospherePlaceholder": "Describe the scene atmosphere...", "environmentalElements": "Environmental Elements", "weather": {"label": "Weather", "selectWeather": "Select weather...", "sunny": "<PERSON>", "cloudy": "Cloudy", "rainy": "Rainy", "stormy": "Stormy", "snowy": "Snowy", "foggy": "Foggy"}, "lighting": {"label": "Lighting", "selectLighting": "Select lighting...", "bright": "<PERSON>", "dim": "<PERSON><PERSON>", "dark": "Dark", "candlelit": "Candlelit", "neon": "Neon"}}, "antecedent": {"title": "Antecedent Layer", "macroHistory": "Macro History", "macroHistoryPlaceholder": "Describe the larger historical context...", "characterPast": "Character Past", "characterPastPlaceholder": "Describe the character's past experiences...", "immediateTrigger": "Immediate Trigger", "immediateTriggerPlaceholder": "Describe the event that triggered this scene..."}}, "subjectives": {"title": "Subjective Elements", "character": {"title": "Character Layer", "mentalModel": "Mental Model", "coreValues": "Core Values", "coreValuesPlaceholder": "Describe the character's core values...", "thinkingMode": "Thinking Mode", "thinkingModePlaceholder": "Describe the character's way of thinking...", "decisionLogic": "Decision Logic", "decisionLogicPlaceholder": "Describe how the character makes decisions...", "emotionalBaseline": "Emotional Baseline", "displayedEmotion": "Displayed Emotion", "displayedEmotionPlaceholder": "Emotions the character shows...", "hiddenEmotion": "Hidden Emotion", "hiddenEmotionPlaceholder": "The character's true inner feelings...", "emotionalIntensity": "Emotional Intensity", "emotionalStability": "Emotional Stability"}, "interaction": {"title": "Interaction Layer", "dialogueStrategy": "Dialogue Strategy", "communicationStyle": "Communication Style", "communicationStylePlaceholder": "Describe the character's communication approach...", "responsePattern": "Response Pattern", "responsePatternPlaceholder": "Describe the character's response habits...", "goalOrientation": "Goal Orientation", "primaryGoal": "Primary Goal", "primaryGoalPlaceholder": "The character's main goal in this scene...", "conflictResolution": "Conflict Resolution", "conflictResolutionPlaceholder": "How the character handles conflicts..."}}, "chapters": {"chapterTitle": "Chapter Title"}}, "buttons": {"createStory": "Create Story"}, "navigation": {"stepOf": "Step {{current}} of {{total}}"}, "validation": {"atLeastOneChapter": "At least one chapter is required to create a story"}, "characterInfo": {"creator": "Creator", "followers": "Followers", "chats": "Chats", "weeklyRank": "Weekly Rank", "stories": "Stories", "satisfaction": "Satisfaction", "rating": "Rating", "defaultDescription": "A vibrant and curious character, full of enthusiasm and kindness."}}, "trophies": {"title": "Achievements", "subtitle": "Showcase your accomplishments and unlock exclusive rewards", "tabs": {"overview": "Overview", "overviewDesc": "Trophy Summary", "achievements": "Achievements", "achievementsDesc": "Chat Depth & Quality", "explorations": "Explorations", "explorationsDesc": "Discovery & Breadth", "social": "Social", "socialDesc": "Community & Sharing", "ranking": "Ranking", "rankingDesc": "Competition & Rankings"}, "categories": {"all": "All", "beginner": "<PERSON><PERSON><PERSON>", "interaction": "Interaction", "creation": "Creation", "collection": "Collection", "social": "Social", "special": "Special Events"}, "rarity": {"bronze": "Bronze", "silver": "Silver", "gold": "Gold", "platinum": "Platinum", "diamond": "Diamond", "legendary": "Legendary"}, "status": {"locked": "Locked", "inProgress": "In Progress", "completed": "Completed", "claimed": "Claimed"}, "filters": {"showAll": "Show All", "showCompleted": "Completed Only", "showInProgress": "In Progress Only", "showLocked": "Locked Only", "sortBy": "Sort By", "sortRarity": "<PERSON><PERSON>", "sortProgress": "Progress", "sortDate": "Date Earned", "sortAlphabetical": "Alphabetical", "status": "Status", "rarity": "<PERSON><PERSON>", "allRarities": "All Rarities", "activeFilters": "Active filters:", "clearAll": "Clear all", "category": "Category:", "search": "Search:", "achievements": {"all": "All", "allDesc": "All achievement types", "conversation": "Conversation", "conversationDesc": "Basic chat interactions", "depth": "De<PERSON><PERSON>", "depthDesc": "Deep conversation achievements", "quality": "Quality", "qualityDesc": "High-quality interaction rewards", "consistency": "Consistency", "consistencyDesc": "Regular engagement bonuses"}, "explorations": {"all": "All", "allDesc": "All exploration types", "characters": "Characters", "charactersDesc": "Character-related discoveries", "creation": "Creation", "creationDesc": "Content creation achievements", "discovery": "Discovery", "discoveryDesc": "New discovery rewards", "collection": "Collection", "collectionDesc": "Collection and completion goals"}, "social": {"all": "All", "allDesc": "All social activities", "community": "Community", "communityDesc": "Community participation", "sharing": "Sharing", "sharingDesc": "Content sharing achievements", "friendship": "Friendship", "friendshipDesc": "Building friendships", "support": "Support", "supportDesc": "Helping others"}, "ranking": {"all": "All", "allDesc": "All ranking achievements", "competitive": "Competitive", "competitiveDesc": "Competitive achievements", "elite": "Elite", "eliteDesc": "Elite player rewards", "legendary": "Legendary", "legendaryDesc": "Legendary accomplishments", "seasonal": "Seasonal", "seasonalDesc": "Seasonal competitions"}}, "stats": {"completionRate": "Completion Rate", "overallProgress": "Overall Progress"}, "overview": {"title": "Trophy Overview", "subtitle": "Your achievement journey across all categories", "totalTrophies": "Total Trophies", "totalPoints": "Total Points", "earnedFromAll": "Earned from all categories", "recentAchievements": "Recent Achievements", "latestUnlocked": "Latest unlocked trophies", "of": "of", "unlocked": "unlocked", "points": "points", "achievements": "Achievements", "achievementsSubtitle": "Chat Depth & Quality", "achievementsDesc": "Deep conversations and meaningful interactions", "explorations": "Explorations", "explorationsSubtitle": "Discovery & Breadth", "explorationsDesc": "Character discovery and content exploration", "social": "Social", "socialSubtitle": "Community & Sharing", "socialDesc": "Community engagement and sharing", "ranking": "Ranking", "rankingSubtitle": "Competition & Rankings", "rankingDesc": "Competitive achievements and leaderboards"}, "leaderboard": {"title": "Global Leaderboard", "topPlayers": "Top Players This Season", "rank": "Rank", "player": "Player", "trophies": "Trophies", "change": "Change"}, "emptyStates": {"achievements": {"title": "No achievements found", "description": "Try adjusting your filters or start engaging in conversations to unlock achievements."}, "explorations": {"title": "No explorations found", "description": "Try adjusting your filters or start exploring to unlock achievements."}, "social": {"title": "No social achievements found", "description": "Try adjusting your filters or start engaging with the community to unlock achievements."}, "ranking": {"title": "No ranking achievements found", "description": "Try adjusting your filters or compete in challenges to unlock achievements."}}, "common": {"progress": "Progress", "of": "of", "points": "points", "pointsEarned": "Points Earned", "claimReward": "<PERSON><PERSON><PERSON>", "close": "Close", "completed": "Completed", "inProgress": "In Progress", "total": "Total", "discovered": "Discovered", "exploring": "Exploring", "achievements": "achievements", "achievement": "achievement", "allAchievements": "All Achievements", "allExplorations": "All Explorations"}, "achievementsTab": {"title": "Achievements", "subtitle": "Master the art of deep conversations and meaningful connections", "conversationMaster": {"title": "Conversation Master", "description": "Engage in meaningful conversations and build lasting connections with AI characters.", "messagesSent": "Messages Sent", "conversations": "Conversations"}, "emotionalDepth": {"title": "Emotional Depth", "description": "Create deep emotional bonds and meaningful memories with characters.", "memoryCapsules": "Memory Capsules", "bondLevel": "Bond Level"}, "consistencyStreak": {"title": "Consistency Streak", "description": "Maintain regular engagement and build lasting habits.", "currentStreak": "Current Streak", "bestStreak": "Best Streak"}}, "explorationsTab": {"title": "Explorations", "subtitle": "Discover new worlds, characters, and hidden treasures", "characterExplorer": {"title": "Character Explorer", "description": "Meet diverse characters and explore different personalities across the platform.", "charactersMet": "Characters Met"}, "creativeExplorer": {"title": "Creative Explorer", "description": "Create and publish your own characters and stories for others to discover.", "createdCharacters": "Created Characters", "publishedStories": "Published Stories"}, "memoryCollector": {"title": "Memory Collector", "description": "Collect precious memories and moments from your adventures.", "memoryCapsules": "Memory Capsules"}, "discoveryMap": {"title": "Discovery Map", "worldsExplored": "Worlds Explored", "secretsFound": "Secrets Found", "storiesDiscovered": "Stories Discovered", "charactersMet": "Characters Met"}}, "socialTab": {"title": "Social", "subtitle": "Connect, share, and build meaningful relationships in the community", "connected": "Connected", "building": "Building", "communityBuilder": {"title": "Community Builder", "description": "Connect with fellow users and build lasting friendships in the community.", "friends": "Friends", "followers": "Followers"}, "contentSharer": {"title": "Content Sharer", "description": "Share your favorite characters and moments with the community.", "shares": "Shares", "likesReceived": "<PERSON><PERSON> Re<PERSON>"}, "communityHelper": {"title": "Community Helper", "description": "Help newcomers and support fellow community members.", "helpfulVotes": "Helpful Votes", "guidesWritten": "Guides Written"}, "socialActivity": {"title": "Social Activity", "receivedLikes": "Received 15 likes on your character share", "newFollowers": "3 new followers joined your community", "helpedNewcomer": "Helped a newcomer with character creation", "hoursAgo": "2 hours ago", "dayAgo": "1 day ago", "daysAgo": "3 days ago"}, "communityStats": {"comments": "Comments", "shares": "Shares", "likesGiven": "Likes Given", "featuredPosts": "Featured Posts"}, "allSocialAchievements": "All Social Achievements"}, "rankingTab": {"title": "Rankings", "subtitle": "Compete for glory and claim your place among the elite", "conquered": "Conquered", "competing": "Competing", "globalRank": {"title": "Global Rank", "outOf": "out of 10,247 players", "champion": "Champion"}, "seasonRank": {"title": "Season Rank", "season": "Season 2024-1", "elite": "Elite"}, "powerLevel": {"title": "Power Level", "description": "Total achievement power", "legendary": "Legendary"}, "competitiveStats": {"competitionsWon": "Competitions Won", "titlesEarned": "Titles Earned", "daysAtTop": "Days at #1", "winStreak": "Win Streak"}, "leaderboard": {"points": "points"}, "allRankingAchievements": "All Ranking Achievements"}, "card": {"progress": "Progress", "reward": "<PERSON><PERSON>", "claimReward": "<PERSON><PERSON><PERSON>", "viewDetails": "View Details", "requirement": "Requirement", "earned": "Earned", "points": "pts"}, "modal": {"achievementDetails": "Achievement Details", "description": "Description", "requirements": "Requirements", "rewards": "Rewards", "progress": "Your Progress", "earnedDate": "Earned on", "tips": "Tips", "relatedAchievements": "Related Achievements", "close": "Close"}, "rewards": {"badge": "Exclusive Badge", "title": "Special Title", "currency": "<PERSON><PERSON><PERSON><PERSON>", "item": "Special Item", "privilege": "Platform Privilege", "experience": "Experience Points"}, "achievements": {"firstSteps": {"name": "First Steps", "description": "Complete your first conversation with an AI character", "requirement": "Send 10 messages in a single conversation", "tips": "Start chatting with any character to begin your journey!"}, "conversationalist": {"name": "Conversationalist", "description": "Master of engaging conversations", "requirement": "Complete 100 total conversations", "tips": "Explore different characters and topics to expand your social skills"}, "memoryKeeper": {"name": "Memory Keeper", "description": "Guardian of precious moments", "requirement": "Create 50 memory capsules", "tips": "Save meaningful conversations as memory capsules"}, "creator": {"name": "Character Creator", "description": "Bring imagination to life", "requirement": "Create your first character", "tips": "Use the character creation tool to design unique personalities"}, "socialite": {"name": "Socialite", "description": "Building bonds across the community", "requirement": "Follow 20 different creators", "tips": "Discover amazing creators in the community section"}, "streakMaster": {"name": "Streak Master", "description": "Consistency is key to greatness", "requirement": "Maintain a 30-day interaction streak", "tips": "Log in daily and complete at least one conversation"}, "explorer": {"name": "Explorer", "description": "Adventurer of infinite possibilities", "requirement": "Cha<PERSON> with 25 different characters", "tips": "Try characters from different genres and personalities"}, "storyteller": {"name": "Storyteller", "description": "Weaver of compelling narratives", "requirement": "Create and publish 5 stories", "tips": "Use the story creation tool to craft engaging narratives"}, "perfectionist": {"name": "Perfectionist", "description": "Excellence in every detail", "requirement": "Receive 100 likes on your characters", "tips": "Focus on character quality and unique personalities"}, "legendary": {"name": "Legendary Creator", "description": "A master recognized by all", "requirement": "Achieve 10,000 total character interactions", "tips": "Create characters that resonate with the community"}}, "underConstruction": {"title": "Coming Soon!", "description": "The achievements system is being crafted with care. Stay tuned for an amazing experience!"}, "empty": {"noAchievements": "No achievements found", "noAchievementsDesc": "Try adjusting your filters or complete more activities to unlock achievements", "startJourney": "Start Your Journey", "startJourneyDesc": "Begin chatting with AI characters to unlock your first achievements!", "noMatchingAchievements": "No matching achievements", "noMatchingDesc": "We couldn't find any achievements that match your current filters. Try adjusting your search criteria or clearing all filters.", "activeFilters": "Active filters:", "clearAllFilters": "Clear All Filters", "yourJourneyAwaits": "Your trophy journey awaits!", "startEngaging": "Start engaging with characters and creating content to unlock your first achievements.", "startChatting": "Start Chatting", "startChattingDesc": "Begin conversations with AI characters to unlock your first achievements", "createContent": "Create Content", "createContentDesc": "Design characters and stories to earn creator achievements"}, "social": {"leaderboard": {"title": "Social Leaderboard", "subtitle": "Compare achievements with friends and discover their specialties", "inviteFriends": "Invite Friends", "friendsLeaderboard": "Friends Leaderboard", "you": "You", "level": "Level {{level}}", "recentAchievements": "Recent Achievements", "challengeFriend": "Challenge Friend", "viewProfile": "View Profile"}, "stats": {"achievements": "Achievements", "points": "Points", "qualityScore": "Quality Score", "completion": "Completion"}, "mockNames": {"masterStoryteller": "Master Storyteller", "socialButterfly": "Social Butterfly", "memoryKeeper": "Memory Keeper", "legendaryCreator": "Legendary Creator"}}, "stories": {"timeline": {"title": "Achievement Stories", "subtitle": "Your journey through achievements told as a visual story", "gallery": "Gallery View", "timeline": "Timeline View", "noStories": "No stories yet", "shareStory": "Share Story", "downloadMoment": "Download Moment", "filterBy": "Filter by", "sortBy": "Sort by", "viewDetails": "View Details"}}, "community": {"title": "Community Hub", "subtitle": "Connect with fellow achievement hunters and share your progress", "newPost": "New Post", "activeChallenges": "Active Challenges", "joinChallenge": "Join Challenge", "joined": "joined", "level": "Level", "reply": "Reply", "tabs": {"allPosts": "All Posts", "guides": "Guides", "challenges": "Challenges", "celebrations": "Celebrations"}, "timeUnits": {"hoursAgo": "{{hours}} hours ago", "daysLeft": "{{days}} days left", "minutesAgo": "{{minutes}} minutes ago"}, "actions": {"like": "Like", "reply": "Reply", "share": "Share"}, "badges": {"expertGuide": "Expert Guide"}, "rewards": {"exclusiveExplorerBadge": "Exclusive Explorer Badge"}, "mockContent": {"streakMasterTip": "🔥 Pro tip for Streak Master: Set up daily reminders and have backup conversation topics ready!"}, "challenges": {"weekendExplorerRush": "Weekend Explorer Rush", "chatWith10Characters": "Cha<PERSON> with 10 different characters this weekend"}}, "personalization": {"settings": "Personalization Settings", "theme": "Theme Preference", "displayMode": "Display Mode", "celebrationStyle": "Celebration Style", "focusCategory": "Primary Focus", "rarityPriority": "Rarity Priority", "previewMode": "Preview Mode", "resetDefaults": "Reset to Defaults", "exportSettings": "Export Settings"}, "views": {"dashboard": "Dashboard", "grid": "Grid", "social": "Social", "stories": "Stories", "community": "Community"}, "dashboard": {"aiRecommendations": {"title": "AI Smart Recommendations", "subtitle": "Personalized suggestions based on your progress and preferences", "reasons": {"almostComplete": "You're almost there! Just a little more effort to complete this achievement.", "timeSensitive": "Time-sensitive! This achievement can be missed if not completed soon.", "quickWin": "Quick win! This can be completed in under an hour."}, "priority": {"high": "high", "medium": "medium", "low": "low"}}, "timeSensitive": {"title": "⚠️ Time-Sensitive Achievements", "subtitle": "These achievements may become unavailable if not completed soon", "missable": "Missable"}, "activeProgress": {"title": "Active Progress", "progress": "Progress"}, "readyToClaim": {"title": "🎉 Ready to Claim!", "pointsEarned": "{{points}} points earned", "claimReward": "<PERSON><PERSON><PERSON>"}, "common": {"mins": "{{time}}min", "pts": "{{points}} pts"}}}, "memory": {"title": "Memory Capsules", "subtitle": "Treasure beautiful moments with your AI companions, making every important memory last forever", "stats": {"total": "Total Memories", "characters": "Characters Involved", "importance": "Avg Importance", "references": "AI References"}, "search": {"placeholder": "Search memories... try \"birthday\", \"promise\", \"dream\"", "aiPowered": "AI Semantic Search"}, "view": {"grid": "Grid View", "timeline": "Timeline View"}, "filter": {"allCharacters": "All Characters", "allEmotions": "All Emotions", "happy": "Happy", "sad": "Touched", "excited": "Excited", "thoughtful": "Thoughtful", "important": "Important", "allTime": "All Time", "today": "Today", "thisWeek": "This Week", "thisMonth": "This Month", "importance": "Importance Filter", "allImportance": "All", "tags": "<PERSON> Filter", "clearTags": "Clear All Tags"}, "time": {"today": "Today", "yesterday": "Yesterday", "daysAgo": "{{days}} days ago"}, "emotion": {"happy": "Happy", "sad": "Touched", "excited": "Excited", "thoughtful": "Thoughtful", "important": "Important"}, "action": {"edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "generateArt": "Generate Memory Art", "share": "Share", "export": "Export", "confirmDelete": "Confirm Delete", "createFirst": "Create First Memory", "learnMore": "Learn More"}, "detail": {"summary": "Memory Summary", "fullDialogue": "Full Dialogue", "importance": "Importance Score", "tags": "Tags", "aiInsights": "AI Insights", "referenceCount": "This memory has been referenced {{count}} times by AI, playing an important role in your conversations with {{character}}."}, "confirm": {"deleteTitle": "Confirm Delete", "deleteMessage": "Are you sure you want to delete this memory? This action cannot be undone."}, "empty": {"noResults": "No matching memories found", "tryDifferent": "Try adjusting your filters or using different search keywords", "title": "Create Your First Memory Capsule", "description": "Permanently preserve beautiful conversations with your AI companions, treasuring every important moment. Memory capsules help AI better understand and remember you.", "goChat": "Start Chatting to Create Memories", "learnMore": "Learn About Memory Capsules"}, "feature": {"smart": "Smart Retrieval", "smartDesc": "AI automatically references relevant memories", "art": "Memory Art", "artDesc": "Transform memories into beautiful artwork", "bond": "<PERSON>en <PERSON>s", "bondDesc": "Help AI companions understand you better"}, "mockData": {"capsuleNames": ["The Birthday Promise", "First Deep Conversation", "Night of Sharing Dreams", "Companionship in Difficult Times", "Happy Memories", "Important Promise", "Plans for the Future", "Late Night Confessions", "Special Anniversary", "Warm Encouragement", "Interesting Discussion", "Life Insights", "Beautiful Wishes", "Unforgettable Experience", "Heartwarming Moment"], "dialogueTopics": ["birthday", "dreams", "future", "growth", "friendship"], "summaryTemplates": ["birthday promise", "life dreams", "future plans", "growth experience", "meaning of friendship"], "tagGroups": [["birthday", "promise", "important"], ["dreams", "future", "planning"], ["growth", "experience", "insight"], ["friendship", "companionship", "warmth"], ["commemoration", "special", "beautiful"]]}}, "storyManagement": {"title": "Manage Stories", "subtitle": "Manage your created stories, view analytics and track performance", "createNew": "Create New Story", "myStories": "My Stories", "searchPlaceholder": "Search stories by title, description, or character...", "stats": {"totalStories": "Total Stories", "totalPlays": "Total Plays", "totalLikes": "Total Likes", "avgRating": "Avg <PERSON>ing"}, "status": {"published": "Published", "draft": "Draft", "archived": "Archived"}, "difficulty": {"easy": "Easy", "normal": "Normal", "hard": "Hard"}, "filter": {"all": "All Status", "published": "Published", "draft": "Draft", "archived": "Archived"}, "table": {"story": "Story", "status": "Status", "plays": "Plays", "likes": "<PERSON>s", "rating": "Rating", "actions": "Actions"}, "actions": {"view": "View Story", "edit": "Edit Story"}, "noStories": "No Stories Yet", "noStoriesDescription": "You haven't created any stories yet. Create your first story to start building interactive experiences.", "createFirstStory": "Create First Story", "noMatches": "No Matching Stories", "noMatchesDescription": "No stories match your search criteria. Try different keywords or clear your search."}, "storyEdit": {"pageTitle": "Edit Story - <PERSON><PERSON>", "pageDescription": "Edit and update your story details, chapters, and settings", "title": "Edit Your Story", "subtitle": "Update and refine your narrative experience for", "success": {"storyUpdated": "Story updated successfully!"}, "error": {"failedToUpdate": "Failed to update story, please try again", "failedToLoad": "Failed to load story data, please try again", "storyNotFound": "Story Not Found", "storyNotFoundDescription": "The story you're trying to edit doesn't exist or has been removed.", "backToStoryManagement": "Back to Story Management"}}, "store": {"title": "Alphane Store", "subtitle": "Enhance your AI companion experience with premium features and digital treasures", "limited": "Limited", "countdown": "Countdown", "categories": {"subscriptions": "Memberships", "currency": "<PERSON><PERSON><PERSON><PERSON>", "characters": "Characters", "items": "Items", "memory": "Memory Arts", "featured": "Featured", "new": "New Arrivals", "memberships": "MemberShip", "welcome": "Welcome", "arts": "Arts", "memorial": "Memorials", "mindfuel": "Mind Fuel"}, "tabs": {"featured": {"label": "Featured", "description": "Top Picks & Deals"}, "memberships": {"label": "MemberShip", "description": "Premium Plans"}, "welcome": {"label": "Welcome", "description": "New User Offers"}, "arts": {"label": "Arts", "description": "Character & Scenes"}, "memorial": {"label": "Memorials", "description": "Special Events"}, "currency": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Tokens & Credits"}, "mindfuel": {"label": "Mind Fuel", "description": "Thought Energy Items"}}, "featured": {"premiumPassOffer": "Premium Pass - Special Offer", "premiumPassDescription": "Unlock unlimited conversations and premium features", "popular": "Popular", "perMonth": "per month", "discount": "50% OFF", "unlimited": "Unlimited", "conversations": "Conversations", "noLimits": "No Limits", "premium": "Premium", "features": "Features", "exclusive": "Exclusive", "creatorBenefits": "Creator Benefits", "currentPrice": "$29.99", "originalPrice": "$39.99", "unlimitedDescription": "100 fuel capacity + 10x ultra-fast recovery", "premiumDescription": "Image + voice multimedia interaction", "exclusiveDescription": "AI companion journal & whisper space", "creatorBenefitsDescription": "20% maximum revenue sharing"}, "subscriptions": {"title": "Membership Plans", "subtitle": "Choose your perfect plan and enjoy unlimited AI experiences", "currentPlan": "Current Plan", "upgrade": "Upgrade", "renew": "<PERSON>w", "cancel": "Cancel", "manage": "Manage Subscription", "benefits": "Benefits", "mostPopular": "Most Popular", "recommended": "Recommended", "monthly": "month", "activeSubscriptions": "Active Subscriptions", "expires": "Expires", "alwaysActive": "Always Active", "active": "Active", "validUntil": "Valid until", "currentlyActive": "Currently Active", "cancelAtNextBilling": "Cancel At Next Billing Cycle", "moreFeatures": "more features", "ultimate": "Ultimate", "free": "Free", "explorer": {"name": "Alphane Explorer", "period": "forever", "features": ["Completely free to use", "Basic AI model conversations", "10 mind fuel capacity limit", "100 memory capsule capacity", "14-day cloud archive protection", "Pay as you go to buy more memory capsule storage space and mind fuel", "Basic community interaction privileges"]}, "pass": {"name": "Alphane Pass", "shortName": "Pass", "features": ["Includes all Alphane Explorer features", "Advanced AI model experience", "30 fuel capacity + 3x recovery speed", "Image sending functionality", "500 memory capsule capacity", "Get 80% off on Conversation Tips", "Permanent data storage during membership period", "Character/story card creation privileges", "Unlock premium daily sign-in gifts", "Dual-track season rewards", "1 Experience card gifting privileges", "Member-exclusive identity badge", "Get 10% off on more memory capsule storage and mind fuel", "24/7 email customer support"]}, "diamond": {"name": "Alphane Diamond", "shortName": "Diamond", "description": "The preferred choice for heavy users, content creators, and business users", "features": ["Includes all Pass features", "100 fuel capacity + 10x ultra-fast recovery", "Image + voice multimedia interaction", "2000 memory capsule capacity", "50 free conversation tips daily", "20% maximum creator revenue sharing", "Three-track season rewards", "3 experience card gifting privileges", "Diamond Member-exclusive identity badge", "AI companion journal and whisper space", "Priority access to new features", "Access to more premium character features", "Get 20% off on more memory capsule storage and mind fuel", "24/7 premium customer support"]}, "infinity": {"name": "Alphane Infinity", "shortName": "Infinity", "description": "The ultimate choice for professional creators and top-tier players", "features": ["Includes all Diamond features", "Unlimited mind fuel to conversations", "Unlimited priority response privileges", "Unlimited memory capsule capacity", "Unlimited free conversation tips", "Higher character/story card creation quota", "50% maximum creator revenue sharing", "Four-track season rewards", "8 experience card gifting privileges", "infinity Member-exclusive identity badge", "Dedicated customer service representative", "Exclusive VIP community"]}, "alphanePass": {"name": "Alphane Pass", "shortName": "Heart Track", "description": "Essential companion features for daily interactions", "price": "$9.99", "period": "month", "features": ["Daily Fast Request privilege (50 requests)", "Daily rewards: 50 Glimmering Dust + 10 Joy Crystals", "Journey premium reward track access", "2 Streak Freeze cards monthly", "Create & share public character cards", "Member exclusive identity badge", "Priority customer support"]}, "diamondPass": {"name": "Alphane Diamond", "shortName": "Diamond Track", "description": "Ultimate AI companion experience with unlimited possibilities", "price": "$19.99", "period": "month", "features": ["Unlimited Fast Request privilege", "Enhanced daily rewards: 150 Dust + 50 Crystals + 1 Memory Puzzle", "Journey diamond collection track access", "5 Streak Freeze cards + 2 free streak repairs monthly", "Creator incentive program (50% revenue share)", "Exclusive Whisper Space access", "Enhanced AI Memory Capsule capacity (3x)", "Priority access to new AI models & features", "Exclusive diamond member privileges", "Premium character creation tools"]}, "limitedTimeOffer": "Limited Time Offer", "upgradeToDiamond": "Upgrade to Diamond", "instantAccess": "Instant Access", "secureBilling": "Secure Billing", "cancelAnytime": "Cancel Anytime", "allFeatures": "All Features"}, "currency": {"title": "Digital Currency", "subtitle": "Power up your interactions with premium currency packages", "yourBalance": "Your Balance", "purchaseEndora": "Purchase Endora", "exchangeCurrencies": "Exchange Currencies", "starEndora": "Star Endora", "purchaseStarEndora": "Purchase Star Endora", "purchaseDescription": "Buy endora with USD - the primary currency for all exchanges", "exchangeTitle": "Exchange Endora for Other Currencies", "exchangeDescription": "Convert your endora into specialized currencies for different features", "availableCurrencies": "Available Currencies", "exchangeForSpecialized": "Exchange endora for specialized currencies", "purchaseRate": "1 USD = 100 Endora", "recharge": "Recharge", "purchase": "Purchase", "bonus": "Bonus", "firstTimeBonus": "First-time bonus", "popular": "Popular", "bestValue": "Best Value", "currencies": {"starEndora": {"name": "Star Endora", "description": "Premium currency purchased with real money"}, "alphane": {"name": "Alphane", "description": "Used for character interactions and special abilities", "uses": "Character conversations, special actions", "exchangeRate": "1 Endora = 10 Alphane"}, "serotile": {"name": "Serotile", "description": "Special tokens for memory creation and customization", "uses": "Memory arts, scene creation, custom content", "exchangeRate": "5 Endora = 1 Serotile"}, "oxytol": {"name": "Oxytol", "description": "Relationship currency for deepening character bonds", "uses": "Relationship progression, intimate scenes, special bonds", "exchangeRate": "2 Endora = 1 Oxytol"}}, "glimmeringDust": {"name": "Glimmering Dust", "description": "Essential currency for daily activities and basic interactions", "uses": "Basic items, gift exchanges, streak freeze card fragments"}, "joyCrystal": {"name": "<PERSON>", "description": "Premium currency for advanced features and exclusive content", "uses": "Memory art generation, premium character gifts, scene unlocks"}, "memoryPuzzle": {"name": "Memory Puzzle", "description": "Rare collectible fragments that unlock special character stories", "uses": "Character backstory unlocks, exclusive artwork, AI memory upgrades"}, "bondDew": {"name": "<PERSON>", "description": "Special currency for deepening relationships with AI characters", "uses": "Character intimacy levels, exclusive interactions, memory slot upgrades"}, "starDiamond": {"name": "Star Diamond", "description": "Premium currency for the most exclusive features and content", "uses": "Premium character cards, exclusive storylines, limited edition items"}, "packages": {"starterBundle": "Starter <PERSON><PERSON><PERSON>", "popularChoice": "Popular Choice", "bestValuePack": "Best Value Pack", "premiumElite": "Premium Elite", "ultimateTreasure": "Ultimate Treasure", "legendaryVault": "<PERSON><PERSON>", "starter": {"name": "Starter Pack", "price": "$4.99", "amount": "300", "bonus": "+50"}, "standard": {"name": "Standard Pack", "price": "$9.99", "amount": "680", "bonus": "+120"}, "premium": {"name": "Premium Pack", "price": "$19.99", "amount": "1480", "bonus": "+320"}, "deluxe": {"name": "Deluxe Pack", "price": "$49.99", "amount": "3980", "bonus": "+1020"}, "ultimate": {"name": "Ultimate Pack", "price": "$99.99", "amount": "8500", "bonus": "+2500"}}}, "characters": {"title": "Premium Characters", "subtitle": "Discover exclusive AI companions crafted by master creators", "official": "Official Collection", "creators": "Creator Spotlight", "limited": "Limited Edition", "featured": "Featured Character", "new": "New Release", "rarity": {"common": "Common", "rare": "Rare", "epic": "Epic", "legendary": "Legendary", "mythic": "Mythic"}, "categories": {"romance": "Romance", "adventure": "Adventure", "fantasy": "Fantasy", "scifi": "Sci-Fi", "slice_of_life": "Daily Life", "mystery": "Mystery"}}, "items": {"title": "Premium Items", "subtitle": "Enhance your experience with special items and customizations", "themes": {"title": "Chat Themes", "description": "Beautiful backgrounds for your conversations"}, "avatars": {"title": "Avatar Frames", "description": "Exclusive frames to showcase your profile"}, "effects": {"title": "Special Effects", "description": "Animated effects for messages and interactions"}, "bundles": {"title": "Bundle Packs", "description": "Special bundles with exclusive items and great savings"}, "gacha": {"title": "Mystery Chests", "description": "Surprise boxes with rare items and collectibles"}, "memory": {"title": "Memory Tools", "description": "Advanced tools for managing AI memories"}}, "memoryArt": {"title": "Memory Art Studio", "subtitle": "Transform precious moments into beautiful AI-generated artwork", "canvases": {"title": "<PERSON>es", "description": "Special canvases for creating memory artwork", "blank": "Blank <PERSON>", "premium": "Premium Memory Canvas", "legendary": "Legendary <PERSON>"}, "styles": {"title": "Art Style Packs", "description": "Exclusive artistic styles for your memory creations", "anime": "Anime Style Pack", "watercolor": "Watercolor Style Pack", "oil": "Oil Painting Style Pack", "digital": "Digital Art Style Pack"}, "workshop": {"title": "Memory Art Workshop", "description": "Generate stunning artwork from your most cherished moments", "create": "Create Memory Art", "gallery": "My Gallery"}}, "cart": {"title": "Shopping Cart", "empty": "Your cart is empty", "emptyDescription": "Explore our store to find amazing items for your AI companion experience", "continueShopping": "Continue Shopping", "total": "Total", "subtotal": "Subtotal", "tax": "Tax", "checkout": "Proceed to Checkout", "remove": "Remove", "quantity": "Quantity", "addToCart": "Add to Cart", "buyNow": "Buy Now"}, "purchase": {"success": "Purchase Successful!", "processing": "Processing Payment...", "failed": "Purchase Failed", "retry": "Retry Payment", "receipt": "View Receipt", "downloadReceipt": "Download Receipt"}, "promotion": {"limitedTime": "Limited Time Offer", "newUser": "New User Special", "flashSale": "Flash Sale", "weekend": "Weekend Deals", "seasonal": "Seasonal Event"}, "filters": {"all": "All Items", "priceRange": "Price Range", "category": "Category", "rarity": "<PERSON><PERSON>", "sortBy": "Sort By", "newest": "Newest First", "popular": "Most Popular", "priceAsc": "Price: Low to High", "priceDesc": "Price: High to Low", "rating": "Highest Rated"}, "wishlist": {"title": "Wishlist", "add": "Add to Wishlist", "remove": "Remove from Wishlist", "empty": "Your wishlist is empty", "emptyDescription": "Add items to your wishlist to keep track of things you want"}, "reviews": {"title": "Reviews", "rating": "Rating", "writeReview": "Write a Review", "helpful": "Helpful", "verified": "Verified Purchase"}, "help": {"title": "Need Help?", "support": "Contact Support", "faq": "Frequently Asked Questions", "refund": "Refund Policy", "security": "Payment Security"}, "welcome": {"title": "Welcome Offers", "loadingOffers": "Loading your exclusive offers...", "limitedTimeSpecials": "Limited Time Specials", "exclusiveRewards": "{{count}} exclusive rewards", "viewDetails": "View Details", "claimRewards": "<PERSON><PERSON><PERSON>", "claimNow": "Claim Now", "maybeLater": "Maybe Later", "claimed": "Claimed", "claiming": "Claiming...", "version": "Version {{version}}", "welcomeBack": "Welcome back to Alphane!", "welcomeBackMessage": "We've missed you! Your last visit was {{date}}. Claim your welcome back rewards before they expire.", "firstTimePurchase": {"title": "First-Time Purchase Special", "subtitle": "Exclusive bonuses for your first purchase", "doubleBonus": "Double Bonus", "extraCharacter": "Extra Character", "vipTrial": "VIP Trial", "claim": "<PERSON><PERSON><PERSON>"}}, "arts": {"categories": {"all": "All Arts", "allDescription": "Browse all available art content", "profile": "Profile Arts", "profileDescription": "Personal avatars & chat bubbles", "character": "Character Arts", "characterDescription": "Character series & collections", "story": "Story Arts", "storyDescription": "Stories & scene artwork", "memory": "Memory Arts", "memoryDescription": "Custom memory creations"}, "rarity": {"common": "Common", "rare": "Rare", "epic": "Epic", "legendary": "Legendary"}, "subTypes": {"avatarFrame": "Avatar Frame", "chatBubble": "<PERSON><PERSON>", "characterSeries": "Character Series", "storyScene": "Story Scene", "environment": "Environment", "memoryArt": "Memory Art"}, "badges": {"new": "NEW", "limited": "LIMITED", "discount": "-{{discount}}%"}, "actions": {"purchase": "Purchase"}, "workshop": {"title": "Art Creation Workshop", "description": "Create custom artwork for your characters and stories using our AI-powered tools", "createCustomArt": "Create Custom Art", "viewGallery": "View Gallery"}}, "memorial": {"activeMemorials": "Active Memorials", "recentlyClaimed": "Recently Claimed", "types": {"first_meeting": "First Meeting Anniversary", "perfect_intimacy": "Perfect Intimacy Anniversary", "perfect_storyline": "Perfect Storyline Anniversary"}, "timeRemaining": {"expired": "Expired", "hoursLeft": "{{hours}}h left", "daysLeft": "{{days}}d left"}, "characterInfo": "{{characterName}} • {{daysCount}} days", "actions": {"claimMemorial": "<PERSON><PERSON><PERSON> Memorial", "processing": "Processing...", "claimed": "Claimed"}}, "currencyCard": {"exchangeCurrency": "Exchange Currency", "uses": "Uses", "currentBalance": "Current Balance", "exchangeRate": "Exchange Rate", "endoraToSpend": "Endora to spend:", "youllReceive": "You'll receive:", "processing": "Processing...", "exchangeNow": "Exchange Now", "popularChoice": "Popular Choice", "bestValue": "Best Value", "limitedTime": "Limited Time", "premiumEndoraDescription": "Premium Star Endora for unlocking exclusive content, characters, and special features", "totalAmount": "Total Amount", "totalValue": "Total Value", "discount": "-{{discount}}%", "purchase": "Purchase", "firstTimeBonus": "First-time bonus", "popular": "Popular", "bestValueTag": "Best Value", "baseEndora": "Base Endora", "bonusEndora": "Bonus Endora", "totalEndora": "Total Endora", "moreValue": "{{percentage}}% more value • {{endoraPerDollar}} Endora per $1", "purchaseNow": "Purchase Now"}, "mindFuelItems": {"title": "Mind Fuel Supply Items", "items": {"small": {"name": "Small Mind Supply", "description": "Restores 1 Mind Fuel point"}, "standard": {"name": "Standard Mind Supply", "description": "Restores 3 Mind Fuel points"}, "premium": {"name": "Premium Mind Supply", "description": "Restores 5 Mind Fuel points"}, "perfect": {"name": "Perfect Mind Supply", "description": "Fully restores Mind Fuel"}}, "rarity": {"common": "Common", "rare": "Rare", "epic": "Epic", "legendary": "Legendary"}, "currency": {"alphane": "Alphane", "endora": "Endora"}, "popular": "Popular", "discount": "-{{discount}}%", "purchase": "Purchase", "purchaseSuccess": "Purchase successful!\n{{name}} x{{quantity}}\nCost: {{price}} {{currency}}"}, "actions": {"subscribeNow": "Subscribe Now", "subscribe": "Subscribe"}, "featuredCard": {"tags": {"quarterlySpecial": "Quarterly Special", "monthlySpecial": "Monthly Special", "weeklySpecial": "Weekly Special", "ipCollab": "IP Collab", "newFeature": "New Feature"}, "collaboration": "Collaboration: {{name}}", "purchaseLimit": "Limit: {{limit}} purchase", "purchaseLimitPlural": "Limit: {{limit}} purchases", "daysLeft": "{{days}} days left", "purchased": "Purchased", "limitReached": "Limit Reached", "remainingPurchases": "{{count}} left", "getWithEndora": "Get with <PERSON><PERSON>", "processing": "Processing...", "confirmPurchase": "Confirm Endora Purchase", "save": "Save {{percentage}}%", "validFor": "Valid for {{days}} days • Endora will be deducted from your balance", "cancel": "Cancel", "ok": "OK"}}, "wallet": {"title": "Wallet", "subtitle": "Manage your account balance and transactions", "gameWallet": "Game Currency <PERSON>et", "currencyWallet": "Digital Currency Wallet", "loading": "Loading wallet data...", "lastUpdated": "Last Updated", "balance": "Balance", "overview": "Overview", "orders": "Orders", "membership": "Membership", "mindFuel": "Mind Fuel", "mindFuelDesc": "Energy for AI conversations", "alphaneDesc": "Essential currency for daily activities", "endoraDesc": "Premium currency for special interactions", "serotileDesc": "Rare fragments for character stories", "oxytolDesc": "Special currency for relationship building", "used24h": "Used 24h", "earned24h": "Earned 24h", "spent24h": "Spent 24h", "nextRecovery": "Next Recovery", "searchOrders": "Search orders...", "allOrders": "All Orders", "noOrders": "No orders found", "noOrdersDescription": "You haven't made any purchases yet. Visit the store to get started!", "currentMembership": "Current Membership", "expiresOn": "Expires On", "mindFuelBonus": "Mind Fuel Bonus", "recoverySpeed": "Recovery Speed", "specialFeatures": "Special Features", "membershipFeatures": "Membership Features", "buyCurrency": "Buy Currency", "buyCurrencyDesc": "Purchase game currencies", "upgradeMembership": "Upgrade Membership", "upgradeMembershipDesc": "Unlock premium benefits", "manageMindFuel": "Manage Mind Fuel", "manageMindFuelDesc": "View usage and recovery", "earnRewards": "<PERSON><PERSON><PERSON>", "earnRewardsDesc": "Complete tasks and challenges", "todayEarned": "Today Earned", "weeklyTrend": "Weekly Trend", "totalEarned": "Total Earned", "current": "Current", "allTime": "All Time", "portfolioValue": "Portfolio Value", "thisWeek": "This Week", "todayTotal": "Today Total", "acrossAllCurrencies": "Across all currencies", "weeklyEarnings": "Weekly Earnings", "bestPerformer": "Best Performer", "currencyBreakdown": "Currency Portfolio Breakdown", "tokens": "tokens", "allTimeEarned": "All Time Earned", "thisWeekEarned": "This Week Earned", "todayEarnings": "Today's Earnings", "earningSources": "Earning Sources", "dailyTasks": "Daily Tasks", "dailyTasksDesc": "Complete daily activities to earn currency rewards", "chatRewards": "<PERSON><PERSON>", "chatRewardsDesc": "Earn currency through conversations with AI characters", "achievements": "Achievements", "achievementsDesc": "Unlock currency bonuses by completing milestones", "specialEvents": "Special Events", "specialEventsDesc": "Participate in limited-time events for bonus rewards", "howToEarn": "How to Earn <PERSON>", "todayProgress": "Today's Progress", "completed": "Completed", "todayChats": "Today's Chats", "conversations": "conversations", "recentUnlock": "Recent Unlock", "currencyTips": "Currency Usage Guide", "alphaneUse": "Basic items, daily activities, streak maintenance", "endoraUse": "Memory art, premium gifts, scene unlocks", "serotileUse": "Character backstories, exclusive artwork, AI memory upgrades", "oxytolUse": "Intimacy levels, exclusive interactions, memory capacity", "totalBalance": "Total Balance", "thisMonth": "This Month", "pending": "Pending", "addFunds": "Add Funds", "withdraw": "Withdraw", "history": "View History", "quickActions": "Quick Actions", "paymentMethods": "Payment Methods", "recentTransactions": "Recent Transactions", "viewAll": "View All", "searchTransactions": "Search transactions...", "noTransactions": "No transactions yet", "noFilteredTransactions": "No transactions found", "loadMore": "Load More", "addPaymentMethod": "Add Payment Method", "default": "<PERSON><PERSON><PERSON>", "selectAmount": "Select Amount", "customAmount": "Custom Amount", "total": "Total", "selectPaymentMethod": "Select Payment Method", "addNewMethod": "Add New Method", "confirmPayment": "Confirm Payment", "confirmDescription": "Please review your payment details before proceeding", "amount": "Amount", "method": "Payment Method", "securePayment": "Secure payment powered by industry-leading encryption", "processing": "Processing...", "getStarted": "Get Started", "status": {"completed": "Completed", "pending": "Pending", "failed": "Failed", "cancelled": "Cancelled"}, "filter": {"all": "All", "income": "Income", "expense": "Expense", "pending": "Pending"}, "empty": {"transactions": "No transactions yet", "transactionsDescription": "Your transaction history will appear here once you start using your wallet", "balance": "No balance available", "balanceDescription": "Add funds to your wallet to get started with purchases and subscriptions", "paymentMethods": "No payment methods", "paymentMethodsDescription": "Add a payment method to easily manage your transactions", "general": "Your wallet is empty", "generalDescription": "Start by adding funds or setting up a payment method"}}, "orders": {"title": "Orders & Transactions", "subtitle": "View your purchase history and game currency transactions", "loading": "Loading orders...", "purchaseOrders": "Purchase Orders", "gameTransactions": "Game Transactions", "searchPlaceholder": "Search orders or transactions...", "allStatuses": "All Statuses", "allTypes": "All Types", "completed": "Completed", "pending": "Pending", "failed": "Failed", "cancelled": "Cancelled", "subscription": "Subscription", "currency": "<PERSON><PERSON><PERSON><PERSON>", "character": "Character", "item": "<PERSON><PERSON>", "earned": "Earned", "spent": "Spent", "purchased": "Purchased", "refunded": "Refunded", "viewDetails": "View Details", "downloadReceipt": "Download Receipt", "noOrders": "No Orders Found", "noOrdersDescription": "You haven't made any purchases yet. Visit the store to get started!", "noTransactions": "No Transactions Found", "noTransactionsDescription": "Your game currency transactions will appear here as you earn and spend currencies."}, "search": {"title": "Search & Discover", "subtitle": "Find characters, stories, memories, and users", "searchPlaceholder": "Search for characters, stories, memories...", "searchPlaceholderShort": "Search...", "searchResults": "Search Results", "noResults": "No results found", "noResultsDescription": "Try different keywords or explore our recommendations", "searching": "Searching...", "clearSearch": "Clear Search", "searchHistory": "Search History", "clearHistory": "Clear History", "recentSearches": "Recent Searches", "popularSearches": "Popular Searches", "searchSuggestions": "Search Suggestions", "quickFilters": "Quick Filters", "advancedFilters": "Advanced Filters", "sortBy": "Sort By", "filterBy": "Filter <PERSON>", "showFilters": "Show Filters", "hideFilters": "Hide Filters", "applyFilters": "Apply Filters", "resetFilters": "Reset Filters", "tabs": {"all": "All", "characters": "Characters", "stories": "Stories", "users": "Users", "memories": "Memories", "creators": "Creators"}, "filters": {"all": "All", "recent": "Recent", "popular": "Popular", "trending": "Trending", "newest": "Newest", "oldest": "Oldest", "relevance": "Relevance", "rating": "Rating", "followers": "Followers", "interactions": "Interactions", "verified": "Verified", "official": "Official", "featured": "Featured", "premium": "Premium", "free": "Free", "category": "Category", "genre": "Genre", "tags": "Tags", "dateRange": "Date Range", "today": "Today", "thisWeek": "This Week", "thisMonth": "This Month", "lastMonth": "Last Month", "thisYear": "This Year", "custom": "Custom Range"}, "categories": {"all": "All Categories", "romance": "Romance", "adventure": "Adventure", "fantasy": "Fantasy", "scifi": "Sci-Fi", "mystery": "Mystery", "horror": "Horror", "comedy": "Comedy", "drama": "Drama", "slice_of_life": "Slice of Life", "historical": "Historical", "anime": "Anime", "games": "Games", "books": "Books", "movies": "Movies", "original": "Original"}, "characters": {"title": "Characters", "subtitle": "{{count}} characters found", "noCharacters": "No characters found", "loadMore": "Load More Characters", "viewProfile": "View Profile", "startChat": "Start Chat", "follow": "Follow", "following": "Following", "followers": "{{count}} followers", "chats": "{{count}} chats", "rating": "{{rating}} rating", "created": "Created {{date}}", "updated": "Updated {{date}}", "by": "by {{creator}}", "official": "Official", "verified": "Verified", "premium": "Premium", "new": "New", "trending": "Trending", "filterByGender": "Filter by Gender", "filterByCategory": "Filter by Category", "filterByTags": "Filter by Tags", "male": "Male", "female": "Female", "other": "Other", "notSpecified": "Not Specified"}, "stories": {"title": "Stories", "subtitle": "{{count}} stories found", "noStories": "No stories found", "loadMore": "Load More Stories", "viewStory": "View Story", "startReading": "Start Reading", "chapters": "{{count}} chapters", "duration": "{{duration}} min read", "likes": "{{count}} likes", "reads": "{{count}} reads", "created": "Created {{date}}", "updated": "Updated {{date}}", "by": "by {{creator}}", "character": "Character: {{name}}", "completed": "Completed", "ongoing": "Ongoing", "draft": "Draft", "filterByStatus": "Filter by Status", "filterByDuration": "Filter by Duration", "short": "Short (< 30 min)", "medium": "Medium (30-60 min)", "long": "Long (> 60 min)"}, "users": {"title": "Users", "subtitle": "{{count}} users found", "noUsers": "No users found", "loadMore": "Load More Users", "viewProfile": "View Profile", "follow": "Follow", "following": "Following", "followers": "{{count}} followers", "characters": "{{count}} characters", "stories": "{{count}} stories", "joined": "Joined {{date}}", "lastActive": "Last active {{date}}", "creator": "Creator", "verified": "Verified", "premium": "Premium Member", "filterByType": "Filter by Type", "filterByActivity": "Filter by Activity", "allUsers": "All Users", "creators": "Creators", "premiumUsers": "Premium Users", "activeUsers": "Active Users", "newUsers": "New Users"}, "memories": {"title": "Memories", "subtitle": "{{count}} memories found", "noMemories": "No memories found", "loadMore": "Load More Memories", "viewMemory": "View Memory", "character": "Character: {{name}}", "created": "Created {{date}}", "emotion": "Emotion: {{emotion}}", "importance": "Importance: {{score}}/10", "tags": "Tags: {{tags}}", "private": "Private", "shared": "Shared", "filterByCharacter": "Filter by Character", "filterByEmotion": "Filter by Emotion", "filterByImportance": "Filter by Importance", "emotions": {"happy": "Happy", "sad": "Sad", "excited": "Excited", "calm": "Calm", "romantic": "Romantic", "nostalgic": "Nostalgic", "surprised": "Surprised", "thoughtful": "Thoughtful", "grateful": "Grateful", "peaceful": "Peaceful"}}, "recommendations": {"title": "Recommendations", "subtitle": "Discover something new", "forYou": "For You", "trending": "Trending Now", "popular": "Popular This Week", "featured": "Featured Content", "newReleases": "New Releases", "becauseYouLiked": "Because you liked {{item}}", "similarTo": "Similar to {{item}}", "basedOnHistory": "Based on your history", "exploreMore": "Explore More", "viewAll": "View All", "refresh": "Refresh Recommendations", "why": "Why this recommendation?", "hideRecommendation": "<PERSON>de", "notInterested": "Not Interested", "reportContent": "Report Content", "easy": "Easy", "medium": "Medium", "hard": "Hard", "premium": "Premium"}, "errors": {"searchFailed": "Search failed. Please try again.", "loadFailed": "Failed to load results. Please try again.", "networkError": "Network error. Check your connection.", "serverError": "Server error. Please try again later.", "noPermission": "You don't have permission to access this content.", "contentNotFound": "Content not found or has been removed."}, "actions": {"search": "Search", "filter": "Filter", "sort": "Sort", "share": "Share", "save": "Save", "bookmark": "Bookmark", "report": "Report", "block": "Block", "follow": "Follow", "unfollow": "Unfollow", "like": "Like", "unlike": "Unlike", "view": "View", "chat": "Cha<PERSON>", "read": "Read", "play": "Play", "download": "Download", "copy": "Copy", "edit": "Edit", "delete": "Delete", "refresh": "Refresh", "loadMore": "Load More", "viewMore": "View More", "showLess": "Show Less", "expand": "Expand", "collapse": "Collapse", "close": "Close", "cancel": "Cancel", "confirm": "Confirm", "ok": "OK", "yes": "Yes", "no": "No", "retry": "Retry", "back": "Back", "next": "Next", "previous": "Previous", "skip": "<PERSON><PERSON>", "finish": "Finish", "done": "Done", "reset": "Reset", "clear": "Clear", "apply": "Apply"}}, "notifications": {"title": "Notifications", "description": "Stay updated on your AI companion journey with personalized notifications", "all": "All", "system": "System", "social": "Social", "profile": "Profile", "subscription": "Subscription", "unreadCount": "{{count}} unread notifications", "inThisCategory": "in this category", "markAllAsRead": "<PERSON> as <PERSON>", "emptyState": {"title": "No {{category}} notifications", "all": "No notifications yet! Your activities and updates will appear here.", "system": "No system notifications. System updates and maintenance alerts will appear here.", "social": "No social notifications. New followers and community activities will appear here.", "profile": "No profile notifications. Profile updates and changes will appear here.", "subscription": "No subscription notifications. Billing and plan updates will appear here."}, "types": {"system": {"systemUpdate": "System Update", "maintenance": "Maintenance", "security": "Security Update", "newFeatures": "New Features", "announcement": "Announcement"}, "social": {"newFollower": "New Follower", "followBack": "Follow Back", "characterLike": "Character Like", "storyLike": "Story Like", "comment": "Comment", "mention": "Mention", "weeklyFollowSummary": "Weekly Follow Summary"}, "profile": {"profileUpdate": "Profile Update", "avatarChange": "Avatar Change", "verification": "Verification", "achievement": "Achievement", "levelUp": "Level Up", "statsUpdate": "Stats Update"}, "subscription": {"renewal": "Subscription Renewal", "expiry": "Expiry Warning", "paymentMethod": "Payment Method", "upgrade": "Upgrade Available", "downgrade": "Downgrade Notice", "billing": "Billing"}}, "actions": {"view": "View", "dismiss": "<PERSON><PERSON><PERSON>", "markAsRead": "<PERSON> <PERSON>", "markAsUnread": "<PERSON> as Unread", "delete": "Delete", "openLink": "Open Link", "goToProfile": "Go to Profile", "goToSettings": "Go to Settings", "goToStore": "Go to Store"}, "time": {"now": "now", "minutesAgo": "{{minutes}} minutes ago", "hoursAgo": "{{hours}} hours ago", "daysAgo": "{{days}} days ago", "weeksAgo": "{{weeks}} weeks ago", "monthsAgo": "{{months}} months ago"}, "messages": {"allRead": "All notifications marked as read", "deleted": "Notification deleted", "error": "Failed to load notifications", "noConnection": "No internet connection", "retry": "Retry"}}, "settings": {"title": "Settings", "subtitle": "Customize your AI companion experience", "unsavedChanges": "You have unsaved changes", "saveChanges": "Save Changes", "discardChanges": "Discard Changes", "saving": "Saving...", "saved": "Setting<PERSON> saved successfully!", "resetToDefaults": "Reset to Defaults", "exportData": "Export Data", "categories": {"account": {"title": "Account & Security", "description": "Manage your account settings and security preferences", "changePassword": "Change Password", "changePasswordDesc": "Update your account password for security", "linkedEmail": "Linked Email", "currentEmail": "Current email: {{email}}", "changeEmail": "Change Email", "twoFactorAuth": "Two-Factor Authentication", "twoFactorAuthDesc": "Add an extra layer of security to your account", "linkedAccounts": "Linked Accounts", "linkedAccountsDesc": "Manage third-party account connections", "deleteAccount": "Delete Account", "deleteAccountDesc": "Permanently delete your account and all data", "requestDeletion": "Request Deletion", "ageVerification": "Age Verification", "ageVerificationDesc": "Verify your age to access all features", "verified": "Verified", "pending": "Pending", "notVerified": "Not Verified", "verify": "Verify Now"}, "aiInteraction": {"title": "AI Interaction", "description": "Configure how you interact with AI characters", "responseSpeed": "Response Speed", "responseSpeedDesc": "Choose your preferred AI response style", "responseSpeedOptions": {"fast": "Fast (Quick responses)", "standard": "Standard (Balanced)", "detailed": "Detailed (Comprehensive)"}, "emotionalIntensity": "Emotional Expression", "emotionalIntensityDesc": "Adjust AI emotional response intensity", "emotionalIntensityOptions": {"subtle": "Subtle (Gentle)", "moderate": "Moderate (Natural)", "rich": "Rich (Expressive)"}, "memorySuggestions": "Memory Suggestions", "memorySuggestionsDesc": "AI suggests saving important conversation moments", "bondingNotifications": "Bonding Notifications", "bondingNotificationsDesc": "Show special animations when intimacy levels increase", "memoryCapacity": "Memory Capacity", "memoryCapacityDesc": "Choose your AI memory capsule capacity", "memoryCapacityOptions": {"basic": "Basic (100 memories)", "enhanced": "Enhanced (500 memories)", "premium": "Premium (Unlimited)"}, "preferredModel": "AI Model", "preferredModelDesc": "Select your preferred AI model", "preferredModelOptions": {"standard": "Standard Model", "gemini_2_5_flash": "Gemini 2.5 Flash", "gemini_2_5_pro": "Gemini 2.5 Pro"}, "autoSaveMemories": "Auto-Save Memories", "autoSaveMemoriesDesc": "Automatically save important conversation moments", "contextAwareness": "Context Awareness", "contextAwarenessDesc": "How well AI remembers conversation context", "contextAwarenessOptions": {"basic": "Basic (Recent messages)", "enhanced": "Enhanced (Session context)", "deep": "Deep (Long-term context)"}, "personalityAdaptation": "Personality Adaptation", "personalityAdaptationDesc": "Allow AI to adapt personality based on interactions", "voicePreference": "Voice Preference", "voicePreferenceDesc": "Choose your preferred interaction mode", "voicePreferenceOptions": {"text_only": "Text Only", "voice_enabled": "Voice Enabled", "voice_preferred": "Voice Preferred"}, "responseLength": "Response Length", "responseLengthDesc": "Preferred length of AI responses", "responseLengthOptions": {"concise": "Concise (Brief)", "balanced": "Balanced (Moderate)", "detailed": "Detailed (Comprehensive)"}, "creativityLevel": "Creativity Level", "creativityLevelDesc": "How creative AI should be in responses", "creativityLevelOptions": {"conservative": "Conservative (Predictable)", "balanced": "Balanced (Moderate)", "creative": "Creative (Imaginative)"}}, "privacy": {"title": "Privacy & Permissions", "description": "Control your privacy and data sharing settings", "profileVisibility": "Profile Visibility", "profileVisibilityDesc": "Choose who can see your profile information", "profileVisibilityOptions": {"public": "Public (Everyone)", "followers_only": "Followers Only", "private": "Private (Only you)"}, "memorySharing": "Memory Sharing", "memorySharingDesc": "Control how your AI memories are used", "memorySharingOptions": {"disabled": "Disabled", "anonymous": "Anonymous (No personal info)", "full": "Full Sharing"}, "digitalTwinInteraction": "Digital Twin Interaction", "digitalTwinInteractionDesc": "Control who can interact with your digital twin", "digitalTwinInteractionOptions": {"anyone": "Anyone", "followers_only": "Followers Only", "disabled": "Disabled"}, "characterAttribution": "Character Attribution", "characterAttributionDesc": "Show your real username on created characters", "dataCollection": "Data Collection", "dataCollectionDesc": "Allow data collection for service improvement", "analyticsOptIn": "Analytics", "analyticsOptInDesc": "Help improve the service by sharing usage analytics", "shareUsageData": "Share Usage Data", "shareUsageDataDesc": "Share anonymous usage data to help improve the service", "allowPersonalization": "Allow Personalization", "allowPersonalizationDesc": "Allow AI to personalize responses based on your preferences", "cookiePreferences": "Cookie Preferences", "cookiePreferencesDesc": "Manage your cookie and tracking preferences", "searchIndexing": "Search Engine Indexing", "searchIndexingDesc": "Allow search engines to index your public content", "socialMediaSharing": "Social Media Sharing", "socialMediaSharingDesc": "Enable sharing content to social media platforms", "locationTracking": "Location Tracking", "locationTrackingDesc": "Allow location-based features and content"}, "notifications": {"title": "Notifications", "description": "Manage your notification preferences", "streakReminders": "Streak Reminders", "streakRemindersDesc": "Remind you to maintain your daily interaction streak", "battlePassProgress": "Battle Pass Progress", "battlePassProgressDesc": "Notifications about battle pass level ups and rewards", "newCharacterReleases": "New Character Releases", "newCharacterReleasesDesc": "Get notified when new official characters are released", "followedCharacterUpdates": "Followed Character Updates", "followedCharacterUpdatesDesc": "Updates from characters you follow", "promotionalOffers": "Promotional Offers", "promotionalOffersDesc": "Receive notifications about sales and special events", "doNotDisturb": "Do Not Disturb", "doNotDisturbDesc": "Set quiet hours when you don't want notifications", "doNotDisturbStart": "Start Time", "doNotDisturbEnd": "End Time", "pushNotifications": "Push Notifications", "pushNotificationsDesc": "Receive notifications on your device", "emailNotifications": "Email Notifications", "emailNotificationsDesc": "Receive notifications via email", "inAppNotifications": "In-App Notifications", "inAppNotificationsDesc": "Show notifications within the app", "weeklyDigest": "Weekly Digest", "weeklyDigestDesc": "Receive weekly summaries of your activity", "maintenanceNotifications": "Maintenance Notifications", "maintenanceNotificationsDesc": "Get notified about system maintenance", "friendActivityNotifications": "Friend Activity", "friendActivityNotificationsDesc": "Notifications about friend interactions", "achievementNotifications": "Achievement Notifications", "achievementNotificationsDesc": "Get notified when you unlock achievements", "memoryMilestones": "Memory Milestones", "memoryMilestonesDesc": "Celebrate special memory moments", "bondingLevelUps": "Bonding Level Ups", "bondingLevelUpsDesc": "Notifications when intimacy levels increase", "taskReminders": "Task Reminders", "taskRemindersDesc": "Remind you about incomplete daily tasks", "premiumExpiryReminders": "Premium Expiry Reminders", "premiumExpiryRemindersDesc": "Reminders about subscription expiration", "notSet": "Not Set", "setTime": "Set Time"}, "display": {"title": "Display & Content", "description": "Customize your app appearance and content preferences", "language": "Interface Language", "languageDesc": "Choose your preferred language", "languageOptions": {"en": "English", "zh": "简体中文", "ja": "日本語"}, "theme": "Theme", "themeDesc": "Choose your preferred color scheme", "themeOptions": {"auto": "Auto (System)", "light": "Light Mode", "dark": "Dark Mode"}, "fontSize": "Font Size", "fontSizeDesc": "Adjust text size for better readability", "fontSizeOptions": {"small": "Small (12px)", "medium": "Medium (14px)", "large": "Large (16px)", "extra_large": "Extra Large (18px)"}, "chatBackground": "Chat <PERSON>", "chatBackgroundDesc": "Customize your chat interface appearance", "customizeBackgrounds": "Customize Backgrounds", "animationLevel": "Animation Level", "animationLevelDesc": "Control interface animations and transitions", "animationLevelOptions": {"none": "None", "reduced": "Reduced", "standard": "Standard", "rich": "<PERSON>"}, "contentFilter": "Content Filter", "contentFilterDesc": "Enable filtering for inappropriate content", "regionalization": "Regionalization", "regionalizationDesc": "Adapt content to your region and culture", "highContrast": "High Contrast", "highContrastDesc": "Increase contrast for better visibility", "reducedMotion": "Reduced Motion", "reducedMotionDesc": "Reduce animations and transitions", "customCssEnabled": "Custom CSS", "customCssEnabledDesc": "Allow custom CSS styling (Premium)", "chatBubbleStyle": "Chat Bubble Style", "chatBubbleStyleDesc": "Choose your preferred chat bubble appearance", "chatBubbleStyleOptions": {"rounded": "Rounded (Friendly)", "square": "Square (Clean)", "minimal": "Minimal (Simple)"}, "messageTimestamps": "Message Timestamps", "messageTimestampsDesc": "Show time stamps on messages", "compactMode": "Compact Mode", "compactModeDesc": "Use a more compact interface layout", "showTypingIndicators": "Typing Indicators", "showTypingIndicatorsDesc": "Show when AI is typing a response"}, "gamification": {"title": "Gamification", "description": "Customize your gaming experience and achievements", "achievementAnimations": "Achievement Animations", "achievementAnimationsDesc": "Show celebration animations when unlocking achievements", "currencyGainNotifications": "Currency Notifications", "currencyGainNotificationsDesc": "Show floating notifications when gaining currency", "taskReminderIntensity": "Task Reminder Intensity", "taskReminderIntensityDesc": "Control how often you're reminded about daily tasks", "taskReminderIntensityOptions": {"low": "Low", "moderate": "Moderate", "high": "High"}, "memoryArtStyle": "Memory Art Style", "memoryArtStyleDesc": "Default art style for memory artwork generation", "memoryArtStyleOptions": {"anime": "Anime Style", "realistic": "Realistic Style", "abstract": "Abstract Style", "custom": "Custom Style"}, "streakMotivation": "Streak Motivation", "streakMotivationDesc": "Receive encouraging messages about your streak", "progressCelebrations": "Progress Celebrations", "progressCelebrationsDesc": "Show special effects when reaching milestones", "competitiveMode": "Competitive Mode", "competitiveModeDesc": "Enable competitive features and leaderboards", "leaderboardVisibility": "Leaderboard Visibility", "leaderboardVisibilityDesc": "Control your visibility on leaderboards", "leaderboardVisibilityOptions": {"public": "Public (Everyone)", "friends": "Friends Only", "private": "Private (Hidden)"}, "autoClaimRewards": "<PERSON>-<PERSON><PERSON><PERSON>", "autoClaimRewardsDesc": "Automatically claim available rewards", "experienceDisplayMode": "Experience Display", "experienceDisplayModeDesc": "How to display experience and progress", "experienceDisplayModeOptions": {"detailed": "Detailed (Show numbers)", "simplified": "Simplified (Progress bars)", "minimal": "Minimal (Just milestones)"}, "badgeDisplayMode": "Badge Display", "badgeDisplayModeDesc": "How to display your achievement badges", "badgeDisplayModeOptions": {"all": "All Badges", "favorites": "Favorites Only", "recent": "Recent Only"}}, "dataManagement": {"title": "Data Management", "description": "Manage your data storage and privacy", "clearCache": "<PERSON>ache", "clearCacheDesc": "Remove temporary files to free up space", "clearNow": "Clear Now", "exportPersonalData": "Export Personal Data", "exportPersonalDataDesc": "Download your chat history and memory capsules", "requestExport": "Request Export", "dataUsageStats": "Data Usage Statistics", "dataUsageStatsDesc": "View your platform usage data and statistics", "viewStats": "View Statistics", "autoMemoryBackup": "Auto Memory Backup", "autoMemoryBackupDesc": "Automatically backup your AI memories to the cloud", "autoCleanup": "Auto Cleanup", "autoCleanupDesc": "Automatically remove old temporary files", "dataRetentionPeriod": "Data Retention", "dataRetentionPeriodDesc": "How long to keep your data (in days)", "cacheSize": "<PERSON><PERSON>", "cacheSizeDesc": "Current cache size: {{size}}MB", "backupFrequency": "Backup Frequency", "backupFrequencyDesc": "How often to backup your data", "backupFrequencyOptions": {"daily": "Daily", "weekly": "Weekly", "monthly": "Monthly"}, "storageOptimization": "Storage Optimization", "storageOptimizationDesc": "Optimize storage by compressing old data", "compressionEnabled": "Data Compression", "compressionEnabledDesc": "Compress data to save storage space", "cloudSyncEnabled": "Cloud Sync", "cloudSyncEnabledDesc": "Sync your data across devices", "localStorageLimit": "Local Storage Limit", "localStorageLimitDesc": "Maximum local storage (MB)", "downloadHistory": "Download History", "downloadHistoryDesc": "Keep track of your data downloads", "chatHistoryLimit": "Chat History Limit", "chatHistoryLimitDesc": "Maximum number of chat messages to keep"}, "premium": {"title": "Premium Features", "description": "Exclusive features for Diamond Pass members", "creatorTools": "C<PERSON>ls", "creatorToolsDesc": "Access advanced character creation and analytics tools", "advancedAnalytics": "Advanced Analytics", "advancedAnalyticsDesc": "Detailed insights into your character performance", "prioritySupport": "Priority Support", "prioritySupportDesc": "Get faster response times from our support team", "exclusiveContent": "Exclusive Content", "exclusiveContentDesc": "Access premium characters and storylines", "whisperSpaceAccess": "Whisper Space Access", "whisperSpaceAccessDesc": "Join exclusive premium member discussions", "unlimitedFastRequests": "Unlimited Fast Requests", "unlimitedFastRequestsDesc": "No limits on fast AI response requests", "enhancedMemoryCapacity": "Enhanced Memory Capacity", "enhancedMemoryCapacityDesc": "3x larger memory capsule capacity", "whisperSpaceSettings": "Whisper Space Settings", "whisperSpaceSettingsDesc": "Manage your premium social privileges", "configureSettings": "Configure <PERSON><PERSON>s", "customUIThemes": "Custom UI Themes", "customUIThemesDesc": "Access to exclusive interface themes", "advancedFilters": "Advanced Filters", "advancedFiltersDesc": "More sophisticated content filtering options", "betaFeatureAccess": "Beta Feature Access", "betaFeatureAccessDesc": "Early access to experimental features", "aiModelSelection": "AI Model Selection", "aiModelSelectionDesc": "Choose from multiple AI models", "customPersonalities": "Custom Personalities", "customPersonalitiesDesc": "Create and use custom AI personalities"}}, "actions": {"save": "Save", "cancel": "Cancel", "reset": "Reset", "export": "Export", "import": "Import", "delete": "Delete", "change": "Change", "configure": "Configure", "manage": "Manage", "upgrade": "Upgrade", "enable": "Enable", "disable": "Disable"}, "messages": {"settingsSaved": "Setting<PERSON> saved successfully!", "settingsReset": "Settings reset to defaults", "exportRequested": "Data export requested. You'll receive an email when ready.", "cacheCleared": "<PERSON><PERSON> cleared successfully", "confirmReset": "Are you sure you want to reset all settings to defaults?", "confirmDelete": "Are you sure you want to delete your account? This action cannot be undone.", "confirmClearCache": "Clear cache to free up storage space?", "upgradeRequired": "This feature requires Alphane Diamond membership", "featureComingSoon": "This feature is coming soon!", "errorSaving": "Error saving settings. Please try again.", "errorLoading": "Error loading settings. Please refresh the page."}}, "square": {"title": "Memory Square", "subtitle": "Explore beautiful memory moments shared by the community", "noMemories": "No memories shared yet", "loadMore": "Load More", "comments": {"title": "Comments", "placeholder": "Share your thoughts...", "submit": "Post", "reply": "Reply", "replyTo": "Reply to @{{name}}", "showReplies": "Show {{count}} replies", "hideReplies": "<PERSON><PERSON> replies", "noComments": "No comments yet. Be the first to comment!", "timeAgo": {"justNow": "Just now", "minutesAgo": "{{minutes}}m ago", "hoursAgo": "{{hours}}h ago", "daysAgo": "{{days}}d ago", "weeksAgo": "{{weeks}}w ago"}}, "actions": {"like": "Like", "comment": "Comment", "share": "Share", "viewCharacter": "View Character", "viewStory": "View Story", "copyLink": "Copy Link", "report": "Report"}, "relatedContent": {"title": "Related Content", "character": "Related Character", "story": "Related Story", "moreFromCharacter": "More from {{name}}", "exploreStory": "Explore Story"}, "stats": {"likes": "{{count}} likes", "comments": "{{count}} comments", "shares": "{{count}} shares"}}, "auth": {"loginTitle": "Sign in to Alphane.ai", "registerTitle": "Create your Alphane.ai account", "needAccount": "Need an account?", "alreadyHaveAccount": "Already have an account?", "byContinuing": "By continuing, you agree to our", "terms": "Terms of Service", "and": "and", "privacy": "Privacy Policy", "password": "Password", "smsEmailCode": "SMS/Email Code", "emailAddress": "Email Address", "verificationCode": "Verification Code", "resend": "Resend", "getCode": "Get Code", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "signingIn": "Signing in...", "continue": "Continue", "or": "OR", "continueWithGoogle": "Continue with Google", "dontHaveAccount": "Don't have an account?", "signUp": "Sign up", "verificationCodeOtp": "Verification Code (OTP)", "confirmPassword": "Confirm Password", "iAgree": "I agree to the", "creatingAccount": "Creating Account...", "errors": {"emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "passwordRequired": "Password is required", "verificationCodeRequired": "Verification code is required", "verificationCodeLength": "Verification code must be 6 digits", "passwordMinLength": "Password must be at least 8 characters", "confirmPassword": "Please confirm your password", "passwordsNotMatch": "Passwords do not match", "agreeTerms": "You must agree to the Terms of Service and Privacy Policy"}, "passwordRepeatPlaceholder": "Repeat password", "emailPlaceholder": "<EMAIL>", "passwordPlaceholder": "Enter your password", "otpPlaceholder": "6-digit code", "passwordMinPlaceholder": "At least 8 characters"}, "flashGenerator": {"title": "AI Story Generator", "subtitle": "Creating an immersive story experience for {{characterName}}", "features": {"sceneSettings": "🎬 Scene Setting", "backgroundContext": "📚 Background Context", "characterPsychology": "🧠 Character Psychology", "dialogueDynamics": "💬 Dialogue Dynamics", "relationshipBuilding": "❤️ Relationship Building", "storyGoals": "🎯 Story Goals"}, "preview": {"title": "✨ Generated Story Preview", "regenerationsLeft": "Regenerations left: {{count}}"}, "chapters": {"title": "📖 Story Chapters", "sceneInfo": "Scene Info", "psychology": "Psychology", "relationships": "Relationships", "goals": "Goals", "chapterTitle": "Chapter {{index}}", "acceptButton": "✅ Accept & Create Story", "regenerateButton": {"text": "🔄 Regenerate ({{count}} left)", "generating": "Regenerating..."}}, "sceneSetting": {"title": "Scene Setting", "environment": "Environment", "location": "Location", "time": "Time", "weather": "Weather"}, "characterPsychology": {"title": "Character Psychology", "coreValues": "Core Values", "displayedEmotion": "Displayed Emotion", "hiddenEmotion": "Hidden Emotion", "thinkingMode": "Thinking Mode"}, "interactionDynamics": {"title": "Interaction Dynamics", "sceneGoal": "Scene Goal", "intimacyLevel": "Intimacy Level", "initiative": "Initiative", "goodwill": "Goodwill"}, "backgroundContext": {"title": "Background Context", "immediateTrigger": "Immediate Trigger", "characterPast": "Character Past"}, "actions": {"accept": "Accept This Story", "regenerate": "Regenerate", "regenerating": "Regenerating...", "copy": "Copy", "copied": "<PERSON>pied"}, "copy": {"chapterTitle": "Chapter {{index}} title", "chapterTitleTooltip": "Copy Chapter {{index}} title"}, "customizeHint": "💡 You can still edit any details in the customize section", "copySuccess": "Copied {{text}}!"}, "storyFlow": {"title": "Story Flow", "description": "Create and organize story chapters", "chapterTitlePlaceholder": "Enter chapter title", "chapterDescriptionPlaceholder": "Brief description of this chapter", "chapterContentPlaceholder": "Chapter content and narrative", "chapters": {"title": "Chapter Management", "addMainChapter": "Add Main Chapter", "addBranchChapter": "Add Branch Chapter", "deleteChapter": "Delete Chapter", "removeChapter": "Remove Chapter", "chapterTitle": "Chapter Title", "chapterDescription": "Chapter Description", "chapterContent": "Chapter Content", "backgroundSetting": "Background Setting", "backgroundSettingPlaceholder": "Describe the setting and atmosphere for this chapter...", "backgroundImage": "Background Image", "uploadBackgroundImage": "Upload Background Image", "backgroundImageUploaded": "Background image uploaded", "clickToChangeImage": "Click to change image", "clickOrDragToUpload": "Click or drag to upload", "useWorldSettingImage": "Use world setting image", "usingWorldSettingImage": "Using world setting image", "inheritedFromWorldSetting": "Inherited from world setting", "completionEffects": "Completion Effects", "bondPointsChange": "Bond Points Change", "bondPointsPlaceholder": "Enter bond points change (e.g., +10, -5, 0)", "greetingChange": "New Greeting Message", "greetingChangePlaceholder": "New greeting after completing this chapter...", "characterMoodChange": "Character Mood Change", "characterMoodChangePlaceholder": "How does the character's mood change?", "customEffects": "Other Effects", "customEffectsPlaceholder": "Any other effects or changes...", "mainChapter": "Main Chapter", "branchChapter": "Branch Chapter", "noChapters": "No chapters yet", "createFirstChapter": "Create first chapter", "addFirstChapter": "Add first chapter", "selectChapter": "Select a Chapter", "selectChapterDescription": "Choose a chapter from the story flow to edit its details", "choices": "Chapter Choices", "addChoice": "Add Choice", "choiceText": "Choice Text", "choiceDescription": "Choice Description", "choiceTriggerCondition": "Trigger Condition", "choiceTriggerConditionPlaceholder": "e.g., Bond level ≥ 3, Has completed previous task...", "nextChapter": "Next Chapter", "selectNextChapter": "Select next chapter...", "flowPreview": "Flow Preview"}, "navigation": {"previous": "Previous", "next": "Next", "continueToObjectives": "Continue to Objectives", "backToWorldSetting": "Back to World Setting"}}, "steps": {"chapters": {"title": "Chapters & Flow", "description": "Create story chapters and set up flow"}, "objectives": {"title": "Objective Elements", "description": "Define objective environmental and background elements of the scene"}, "subjectives": {"title": "Subjective Elements", "description": "Configure character psychology and interaction dynamics"}}, "buttons": {"createStory": "Create Story"}, "validation": {"atLeastOneChapter": "At least one chapter is required to create a story"}, "characterProfile": {"header": {"followers": "Followers", "chats": "Chats", "weeklyRank": "Weekly Rank", "interactions": "Interactions", "memories": "Memories", "relationshipLevel": "Relationship Level", "creatorLabel": "Creator"}, "description": {"default": "<PERSON><PERSON> is a vibrant and curious girl, full of childlike innocence and enthusiasm. She loves exploring the unknown, making new friends, and inspiring everyone around her with her optimism and kindness."}, "stats": {"todaysRewards": "Today's Rewards", "interactionStreak": "Interaction Streak", "dailyTasks": "Daily Tasks", "completed": "completed", "days": "days", "closeFriend": "Close Friend", "relationship": "Relationship"}, "actions": {"startChat": "Start Chat", "follow": "Follow"}, "tabs": {"memories": "Memories", "stories": "Stories", "badges": "Badges"}, "badges": {"daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "totalChats": "Total Chats", "interactionTime": "Interaction Time", "intimacyGrowth": "Intimacy Growth", "memoriesCreated": "Memories Created"}, "stories": {"official": "Official", "community": "Community", "chapters": "Ch", "moments": "Moments", "completed": "Completed", "ongoing": "Ongoing", "reads": "reads"}}, "profile": {"redirect": {"message": "Redirecting to your profile..."}, "header": {"followers": "Followers", "following": "Following", "moments": "Moments", "memories": "Memories", "liked": "Liked", "links": "Links"}, "description": {"default": "Passionate about crafting immersive AI characters and captivating stories. Building worlds where imagination meets technology."}, "actions": {"follow": "Follow", "message": "Message"}, "tabs": {"moments": "Moments", "likes": "<PERSON>s", "friends": "Friends"}, "errors": {"userNotFound": "User Not Found", "userNotFoundDesc": "The user you're looking for doesn't exist."}, "anonymous": "Anonymous"}, "creatorProfile": {"redirect": {"message": "Redirecting to your creator profile..."}, "header": {"creatorBadge": "👑 Creator", "followers": "Followers", "following": "Following", "characters": "Characters", "stories": "Stories", "earnings": "Earnings", "links": "Links"}, "description": {"default": "Passionate about crafting immersive AI characters and captivating stories. Building worlds where imagination meets technology."}, "actions": {"follow": "Follow", "message": "Message"}, "tabs": {"characters": "Characters", "stories": "Stories", "dashboard": "Dashboard"}, "dashboard": {"totalEarnings": "Total Earnings", "followers": "Followers", "totalLikes": "Total Likes", "thisMonth": "This Month", "topCharacters": "Top Characters", "onlyVisibleToCreator": "Dashboard is only visible to the creator."}, "stories": {"published": "published", "draft": "draft", "reads": "reads", "chapters": "chapters"}, "errors": {"creatorNotFound": "Creator Not Found", "creatorNotFoundDesc": "The creator you're looking for doesn't exist."}, "anonymous": "Anonymous", "currencyGuide": {"title": "Currency Guide", "subtitle": "Learn about different currencies and how to earn them", "buyCurrency": "Buy Currency", "buyCurrencyDesc": "Purchase game currency", "upgradeMembership": "Upgrade Membership", "upgradeMembershipDesc": "Unlock premium benefits", "manageMindFuel": "Manage Mind Fuel", "manageMindFuelDesc": "View usage and recovery status", "earnRewards": "<PERSON><PERSON><PERSON>", "earnRewardsDesc": "Complete tasks and challenges", "howToEarn": "How to <PERSON>arn <PERSON>", "currencyTips": "Currency Usage Guide", "alphaneDesc": "Daily activity currency", "endoraDesc": "Premium interaction currency", "serotileDesc": "Character story fragments", "oxytolDesc": "Relationship building currency", "mindFuelDesc": "Mind Fuel (energy for conversations)", "alphaneUse": "Used for daily task rewards, chat bonuses and basic interactions", "endoraUse": "Used for premium interactions, special events and exclusive content", "serotileUse": "Collect to unlock character story fragments and narrative experiences", "oxytolUse": "Invest to deepen relationships and build stronger bonds", "dailyTasks": "Daily Tasks", "dailyTasksDesc": "Complete daily activities for steady rewards", "chatRewards": "<PERSON><PERSON>", "chatRewardsDesc": "Earn currency through engaging conversations", "achievements": "Achievements", "achievementsDesc": "Complete various milestones to unlock rewards", "specialEvents": "Special Events", "specialEventsDesc": "Participate in limited-time events for extra rewards"}}, "story": {"title": "Story Details", "header": {"back": "Back", "continueStory": "Continue Story"}, "info": {"starring": "Starring", "starring2": "", "estimatedDuration": "Estimated playtime: ", "creator": "Creator: ", "plays": " plays", "likes": " likes", "rating": " rating", "progress": "Story Progress", "chapters": " chapters", "completed": "completed", "current": "current"}, "actions": {"like": "Like", "share": "Share", "favorite": "Favorite"}, "sections": {"description": "Story Description", "tags": "Story Tags", "openingMessage": "Opening Message", "chapterProgress": "Chapter Progress", "keyChoices": "Key Choices", "completionRewards": "Completion Rewards", "unlockableAchievements": "Unlockable Achievements", "participatingCharacters": "Participating Characters", "unlockConditions": "Unlock Conditions"}, "chapters": {"title": "Chapter Progress", "enterChapter": "Enter chapter: ", "completed": "Completed", "current": "Current", "locked": "Locked"}, "choices": {"title": "Key Choices", "description": "In Chapter {{chapter}}, you will face important branching choices:", "result": "Result: "}, "rewards": {"moreRewards": "{{count}} more rewards..."}, "achievements": {"title": "Unlockable Achievements"}, "characters": {"title": "Participating Characters", "mainRole": "Main Character", "supportingRole": "Supporting Character", "minorRole": "Minor Character", "mainDescription": ", lively and cheerful", "supportingDescription": ", key story figure", "bondLevel": "Bond Lv.{{level}}", "unlocked": "Unlocked", "locked": "Locked"}, "unlock": {"title": "Unlock Conditions", "bondLevel": "Bond level with {{character}} reaches Level {{level}}", "tutorialComplete": "Complete tutorial", "monthlyPass": "Own monthly pass privileges (recommended)"}, "currency": {"fire": "🔥", "diamond": "💎", "puzzle": "🧩", "drop": "💧"}}}